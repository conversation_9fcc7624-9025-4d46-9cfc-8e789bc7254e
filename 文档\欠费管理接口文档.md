# 欠费管理接口文档

## 概述
欠费管理模块提供了完整的欠费记录管理功能，包括欠费记录查询、删除、核销等操作。

## 接口列表

### 1. 获取欠费记录列表
**接口地址：** `GET /overdueRecords/index`

**请求参数：**
```json
{
  "page": 1,                    // 页码
  "limit": 10,                  // 每页数量
  "heatUnitId": 1,             // 小区ID（可选）
  "roomNo": "101",             // 房号（可选）
  "heatingYear": 2024,         // 供暖年度（可选）
  "status": ["active"],        // 状态数组（可选）
  "minOverdueAmount": 100.00,  // 最小欠费金额（可选）
  "maxOverdueAmount": 1000.00, // 最大欠费金额（可选）
  "minOverdueDays": 30,        // 最小逾期天数（可选）
  "maxOverdueDays": 90,        // 最大逾期天数（可选）
  "firstOverdueStartDate": "2024-01-01", // 首次逾期开始日期（可选）
  "firstOverdueEndDate": "2024-12-31"    // 首次逾期结束日期（可选）
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "billId": 123,
        "houseId": 456,
        "heatingYear": 2024,
        "totalAmount": 2000.00,
        "paidAmount": 1500.00,
        "overdueAmount": 500.00,
        "overdueDays": 45,
        "penaltyRate": 0.0005,
        "penaltyAmount": 11.25,
        "status": "active",
        "firstOverdueDate": "2024-02-01",
        "lastUpdatedDate": "2024-03-15",
        "createdAt": "2024-02-01 10:30:00",
        "updatedAt": "2024-03-15 10:30:00",
        "roomNo": "101",
        "heatUnitName": "阳光小区",
        "houseName": "张三",
        "billNo": "BILL202401001",
        "dueDate": "2024-01-31"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 删除欠费记录
**接口地址：** `DELETE /overdueRecords/delete/{ids}`

**请求参数：**
- ids: 记录ID数组，如 `1,2,3`

**返回结果：**
```json
{
  "code": 0,
  "msg": "删除成功"
}
```

### 3. 获取欠费记录详情
**接口地址：** `GET /overdueRecords/info/{id}`

**请求参数：**
- id: 记录ID

**返回结果：**
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "billId": 123,
    "houseId": 456,
    "heatingYear": 2024,
    "totalAmount": 2000.00,
    "paidAmount": 1500.00,
    "overdueAmount": 500.00,
    "overdueDays": 45,
    "penaltyRate": 0.0005,
    "penaltyAmount": 11.25,
    "status": "active",
    "firstOverdueDate": "2024-02-01",
    "lastUpdatedDate": "2024-03-15",
    "createdAt": "2024-02-01 10:30:00",
    "updatedAt": "2024-03-15 10:30:00"
  }
}
```

### 4. 核销欠费记录
**接口地址：** `POST /overdueRecords/writeOff`

**请求参数：**
```json
{
  "id": 1,
  "reason": "特殊情况核销"   // 核销原因（可选）
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "核销成功"
}
```

### 5. 批量核销欠费记录
**接口地址：** `POST /overdueRecords/batchWriteOff`

**请求参数：**
```json
{
  "ids": [1, 2, 3],
  "reason": "批量核销原因"   // 核销原因（可选）
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "批量核销成功"
}
```

## 状态说明
- `active`: 生效中（正常的欠费状态）
- `cleared`: 已结清（用户已缴费）
- `written_off`: 已核销（管理员核销处理）

## 业务规则
1. 只有状态为"生效中"的记录才能删除和核销
2. 已结清或已核销的记录不能修改
3. 核销操作不可逆，需要谨慎操作
4. 支持单个核销和批量核销
5. 欠费金额 = 账单总金额 - 已缴金额
6. 滞纳金 = 欠费金额 × 滞纳金日利率 × 逾期天数

## 数据关联
- 关联t_bill表获取账单信息
- 关联t_house表获取房屋信息
- 关联t_heat_unit表获取小区信息
- 所有关联数据在后端查询时一次性获取
