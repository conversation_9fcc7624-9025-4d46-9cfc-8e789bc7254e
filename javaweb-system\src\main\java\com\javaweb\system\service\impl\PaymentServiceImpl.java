package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.Bill;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.Payment;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.mapper.PaymentMapper;
import com.javaweb.system.query.PaymentQuery;
import com.javaweb.system.service.IBillService;
import com.javaweb.system.service.IHouseInfoService;
import com.javaweb.system.service.IPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 缴费记录表 服务实现类
 * </p>
 */
@Service
public class PaymentServiceImpl extends ServiceImpl<PaymentMapper, Payment> implements IPaymentService {

    @Autowired
    private PaymentMapper paymentMapper;
    
    @Autowired
    private IBillService billService;
    
    @Autowired
    private IHouseInfoService houseInfoService;

    @Autowired
    private HeatUnitMapper heatUnitMapper; // 小区信息数据访问对象

    /**
     * 获取缴费记录列表
     * @param paymentQuery 查询条件
     * @return
     */
    @Override
    public JsonResult getList(PaymentQuery paymentQuery) {

        // 判断是否有房屋相关的查询条件
        boolean hasHouseCondition =
                paymentQuery.getHeatUnitId() != null && paymentQuery.getHeatUnitId() > 0 ||
                        paymentQuery.getHeatUnitFloorId() != null && paymentQuery.getHeatUnitFloorId() > 0 ||
                        paymentQuery.getHeatUnitFloorUnitId() != null && paymentQuery.getHeatUnitFloorUnitId() > 0 ||
                        StringUtils.isNotEmpty(paymentQuery.getRoomNo());

        List<HouseInfo> houseList = Collections.emptyList();
        if (hasHouseCondition) {
            QueryWrapper<HouseInfo> queryWp = new QueryWrapper<>();
            if (paymentQuery.getHeatUnitId() != null && paymentQuery.getHeatUnitId() > 0) {
                queryWp.eq("heat_unit_id", paymentQuery.getHeatUnitId());
            }
            if (paymentQuery.getHeatUnitFloorId() != null && paymentQuery.getHeatUnitFloorId() > 0) {
                queryWp.eq("heat_unit_floor_id", paymentQuery.getHeatUnitFloorId());
            }
            if (paymentQuery.getHeatUnitFloorUnitId() != null && paymentQuery.getHeatUnitFloorUnitId() > 0) {
                queryWp.eq("heat_unit_floor_unit_id", paymentQuery.getHeatUnitFloorUnitId());
            }
            if (StringUtils.isNotEmpty(paymentQuery.getRoomNo())) {
                queryWp.like("room_no", paymentQuery.getRoomNo());
            }
            if (StringUtils.isNotEmpty(paymentQuery.getHouseMaster())) {
                queryWp.like("house_master", paymentQuery.getHouseMaster());
            }
            houseList = houseInfoService.list(queryWp);
        }
        // 查询条件
        QueryWrapper<Payment> queryWrapper = new QueryWrapper<>();
        // 支付方式
        if (StringUtils.isNotEmpty(paymentQuery.getPaymentMethod())) {
            queryWrapper.eq("payment_method", paymentQuery.getPaymentMethod());
        }

        // 缴费日期范围
        if (StringUtils.isNotEmpty(paymentQuery.getStartDate()) && StringUtils.isNotEmpty(paymentQuery.getEndDate())) {
            queryWrapper.between("payment_date", paymentQuery.getStartDate(), paymentQuery.getEndDate());
        }
        // 供暖年度
        if (paymentQuery.getHeatYear() != null) {
            queryWrapper.eq("heat_year", paymentQuery.getHeatYear());
        }
        // 如果有 houseList，则添加 house_id 条件
        if (!houseList.isEmpty()) {
            List<Integer> houseIds = houseList.stream()
                    .map(HouseInfo::getId)
                    .collect(Collectors.toList());
            queryWrapper.in("house_id", houseIds);
        }
        queryWrapper.orderByDesc("id");
        // 查询分页数据
        IPage<Payment> page = new Page<>(paymentQuery.getPage(), paymentQuery.getLimit());
        
        // 这里实际需要自定义SQL查询，因为要关联多个表
        // 简化处理，直接使用默认查询
        IPage<Payment> pageData = paymentMapper.selectPage(page, queryWrapper);
        
        // 处理房屋和账单信息，实际项目中应在SQL中关联查询
        pageData.getRecords().forEach(payment -> {
            // 获取房屋信息
            HouseInfo houseInfo = houseInfoService.getById(payment.getHouseId());
            if (houseInfo != null) {
                payment.setRoomNo(houseInfo.getRoomNo());
                payment.setHouseName(houseInfo.getHouseMaster());
                payment.setHeatUnitId(houseInfo.getHeatUnitId());
                HeatUnit heatUnit = heatUnitMapper.selectById(houseInfo.getHeatUnitId());
                payment.setHeatUnitName(heatUnit.getName());
            }
            
            // 获取账单信息
            Bill bill = billService.getById(payment.getBillId());
            if (bill != null) {
                payment.setBillInfo(bill.getHeatYear() + "年供暖账单");
            }
        });
        
        return JsonResult.success(pageData);
    }

    /**
     * 生成交易流水号
     * 格式：年月日时分秒 + 4位随机数
     * @return 交易流水号
     */
    private String generateTransactionNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateStr = sdf.format(new Date());
        
        // 生成4位随机数
        Random random = new Random();
        int randomNum = random.nextInt(10000);
        String randomStr = String.format("%04d", randomNum);
        
        return "TR" + dateStr + randomStr;
    }

    /**
     * 添加或编辑缴费记录
     * @param entity 实体对象
     * @return
     */
    @Override
    @Transactional
    public JsonResult edit(Payment entity) {
        if (entity == null) {
            return JsonResult.error("参数错误");
        }
        
        // 验证账单
        Bill bill = billService.getById(entity.getBillId());
        if (bill == null) {
            return JsonResult.error("账单不存在");
        }
        entity.setHeatYear(bill.getHeatYear());
        // 验证房屋
        HouseInfo houseInfo = houseInfoService.getById(entity.getHouseId());
        if (houseInfo == null) {
            return JsonResult.error("房屋信息不存在");
        }
        
        // 如果是新增且交易流水号为空，则自动生成
        if (entity.getId() == null && StringUtils.isEmpty(entity.getTransactionNo())) {
            entity.setTransactionNo(generateTransactionNo());
        }

        boolean result = saveOrUpdate(entity);
        if (result) {
            return JsonResult.success("保存成功");
        } else {
            return JsonResult.error("保存失败");
        }
    }

    /**
     * 删除缴费记录
     * @param ids ID数组
     * @return
     */
    @Override
    @Transactional
    public JsonResult deleteByIds(Integer[] ids) {
        if (ids == null || ids.length == 0) {
            return JsonResult.error("参数错误");
        }
        
        for (Integer id : ids) {
            // 获取缴费记录
            Payment payment = getById(id);
            if (payment == null) {
                continue;
            }

            // 先更新账单已缴金额（减去被删除的缴费记录金额）
            billService.updatePaidAmount(payment.getBillId(), payment.getAmount().negate(),true);
            
            // 删除缴费记录
            removeById(id);
        }
        
        return JsonResult.success("删除成功");
    }

    /**
     * 根据ID获取缴费记录详情
     * @param id 缴费记录ID
     * @return
     */
    @Override
    public JsonResult info(Integer id) {
        Payment payment = getById(id);
        if (payment == null) {
            return JsonResult.error("缴费记录不存在");
        }
        
        // 获取房屋信息
        HouseInfo houseInfo = houseInfoService.getById(payment.getHouseId());
        if (houseInfo != null) {
            payment.setRoomNo(houseInfo.getRoomNo());
            payment.setHouseName(houseInfo.getHouseMaster());
            payment.setHeatUnitId(houseInfo.getHeatUnitId());
            payment.setHeatUnitFloorId(houseInfo.getHeatUnitFloorId());
            payment.setHeatUnitFloorUnitId(houseInfo.getHeatUnitFloorUnitId());
        }
        
        // 获取账单信息
        Bill bill = billService.getById(payment.getBillId());
        if (bill != null) {
            payment.setBillInfo(bill.getHeatYear() + "年供暖账单");
        }
        
        return JsonResult.success(payment);
    }
    
    /**
     * 根据住户地址/户号或户主姓名搜索住户信息
     * @param heatUnitId 小区ID
     * @param addressOrRoomNo 住户地址或户号
     * @param ownerName 户主姓名
     * @return 住户信息
     */
    @Override
    public JsonResult searchHouse(Integer heatUnitId, String addressOrRoomNo, String ownerName) {
        QueryWrapper<HouseInfo> queryWrapper = new QueryWrapper<>();
        
        boolean hasCondition = false;
        
        // 添加小区ID条件
        if (heatUnitId != null && heatUnitId > 0) {
            queryWrapper.eq("heat_unit_id", heatUnitId);
            hasCondition = true;
        }

        // 添加房屋地址或户号条件
        if (StringUtils.isNotEmpty(addressOrRoomNo)) {
            queryWrapper.like("room_no", addressOrRoomNo);
            hasCondition = true;
        }
        
        // 添加户主姓名条件
        if (StringUtils.isNotEmpty(ownerName)) {
            queryWrapper.like("house_master", ownerName);
            hasCondition = true;
        }
        
        // 检查是否有搜索条件
        if (!hasCondition) {
            return JsonResult.error("请提供至少一个搜索条件");
        }
        
        // 限制查询结果数量，避免返回过多数据
        queryWrapper.last("LIMIT 1");
        
        // 执行查询
        List<HouseInfo> houseList = houseInfoService.list(queryWrapper);
        
        if (houseList == null || houseList.isEmpty()) {
            return JsonResult.error("未找到相关住户信息");
        }
        
        // 获取第一个住户信息（可根据需求修改为返回列表供选择）
        HouseInfo houseInfo = houseList.get(0);
        
        // 查询该住户当前年度的账单
        QueryWrapper<Bill> billQueryWrapper = new QueryWrapper<>();
        billQueryWrapper.eq("house_id", houseInfo.getId());
        // 按年度降序排序，获取最新年度的账单
        billQueryWrapper.orderByDesc("heat_year");
        billQueryWrapper.last("LIMIT 1");
        
        Bill currentBill = billService.getOne(billQueryWrapper);
        
        // 获取小区名称
        HeatUnit heatUnit = null;
        if (houseInfo.getHeatUnitId() != null) {
            heatUnit = heatUnitMapper.selectById(houseInfo.getHeatUnitId());
        }
        
        // 构建返回数据
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("houseId", houseInfo.getId());
        resultMap.put("heatUnitId", houseInfo.getHeatUnitId());
        resultMap.put("heatUnitName", heatUnit != null ? heatUnit.getName() : "");
        resultMap.put("roomNo", houseInfo.getRoomNo());
        resultMap.put("houseMaster", houseInfo.getHouseMaster());
        resultMap.put("billableArea", houseInfo.getBillableArea());
        // 使用房号作为地址
        resultMap.put("address", houseInfo.getRoomNo());
        
        if (currentBill != null) {
            resultMap.put("currentBillId", currentBill.getId());
            resultMap.put("heatYear", currentBill.getHeatYear());
            resultMap.put("totalAmount", currentBill.getTotalAmount());
            resultMap.put("paidAmount", currentBill.getPaidAmount());
            resultMap.put("status", currentBill.getStatus());
            resultMap.put("dueDate", currentBill.getDueDate());
        }
        
        return JsonResult.success(resultMap);
    }
    
    /**
     * 获取指定住户的缴费记录
     * @param heatUnitId 小区ID
     * @param roomNo 房间号
     * @return 缴费记录列表
     */
    @Override
    public JsonResult getPaymentRecords(Integer heatUnitId,String ownerName, String roomNo) {
        // 先根据小区、楼宇、单元、房间号查询住户信息
        QueryWrapper<HouseInfo> houseQueryWrapper = new QueryWrapper<>();
        houseQueryWrapper.eq("heat_unit_id", heatUnitId)
                        .eq("room_no", roomNo)
                         .like("house_master", ownerName);
        
        HouseInfo houseInfo = houseInfoService.getOne(houseQueryWrapper);
        
        if (houseInfo == null) {
            return JsonResult.error("未找到住户信息");
        }
        
        // 查询该住户的缴费记录
        QueryWrapper<Payment> paymentQueryWrapper = new QueryWrapper<>();
        paymentQueryWrapper.eq("house_id", houseInfo.getId());
        // 按缴费时间降序排序
        paymentQueryWrapper.orderByDesc("payment_date");
        
        List<Payment> paymentList = list(paymentQueryWrapper);
        
        // 处理缴费记录的关联信息
        for (Payment payment : paymentList) {
            // 获取账单信息
            Bill bill = billService.getById(payment.getBillId());
            if (bill != null) {
                payment.setBillInfo(bill.getHeatYear() + "年供暖账单");
            }
        }
        
        return JsonResult.success(paymentList);
    }

    @Override
    public JsonResult getPaymentRecordsByBillId(Integer billId) {

        // 查询该住户的缴费记录
        QueryWrapper<Payment> paymentQueryWrapper = new QueryWrapper<>();
        paymentQueryWrapper.eq("bill_id", billId);
        // 按缴费时间降序排序
        paymentQueryWrapper.orderByDesc("payment_date");

        List<Payment> paymentList = list(paymentQueryWrapper);
        return JsonResult.success(paymentList);
    }

    /**
     * 更新账单和房屋供热状态
     * 缴费成功后更新对应账单的已缴金额和房屋的供热状状态
     *
     * @param payment 缴费记录
     * @return 更新结果
     */
    @Override
    @Transactional
    public JsonResult updateBillAndHouseStatus(Payment payment) {
        try {
            // 1. 更新账单已缴金额
            JsonResult billUpdateResult = billService.updatePaidAmount(
                payment.getBillId(),
                payment.getAmount(),
                payment.getIsHeating()
            );

            if (billUpdateResult.getCode() != 0) {
                return billUpdateResult;
            }

            // 2. 获取账单信息，检查是否需要更新房屋供热状态
            Bill bill = billService.getById(payment.getBillId());
            if (bill == null) {
                return JsonResult.error("账单信息不存在");
            }

            // 3. 更新房屋供热状态
            JsonResult houseUpdateResult = updateHouseHeatingStatus(payment.getHouseId(), bill);
            if (houseUpdateResult.getCode() != 0) {
                return houseUpdateResult;
            }

            return JsonResult.success("更新账单和房屋状态成功");

        } catch (Exception e) {
            System.err.println("更新账单和房屋状态失败: " + e.getMessage());
            return JsonResult.error("更新账单和房屋状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新房屋供热状态
     * 根据账单缴费情况决定房屋是否供热
     *
     * @param houseId 房屋ID
     * @param bill 账单信息
     * @return 更新结果
     */
    private JsonResult updateHouseHeatingStatus(Integer houseId, Bill bill) {
        try {
            // 获取房屋信息
            HouseInfo houseInfo = houseInfoService.getById(houseId);
            if (houseInfo == null) {
                return JsonResult.error("房屋信息不存在");
            }

            // 判断是否应该供热
            boolean shouldHeat = shouldProvideHeating(bill);

            // 更新房屋供热状态
            Integer newHeatingStatus = shouldHeat ? 1 : 0;
            if (!newHeatingStatus.equals(houseInfo.getIsHeating())) {
                houseInfo.setIsHeating(newHeatingStatus);
                boolean updateSuccess = houseInfoService.updateById(houseInfo);

                if (updateSuccess) {
                    System.out.println("成功更新房屋[" + houseId + "]供热状态为: " +
                                     (shouldHeat ? "供热" : "不供热"));
                } else {
                    return JsonResult.error("更新房屋供热状态失败");
                }
            }

            return JsonResult.success("房屋供热状态更新成功");

        } catch (Exception e) {
            System.err.println("更新房屋供热状态异常: " + e.getMessage());
            return JsonResult.error("更新房屋供热状态异常: " + e.getMessage());
        }
    }

    /**
     * 判断是否应该提供供热服务
     * 根据账单缴费情况和业务规则判断
     *
     * @param bill 账单信息
     * @return 是否应该供热
     */
    private boolean shouldProvideHeating(Bill bill) {
        if (bill == null) {
            return false;
        }

        // 获取已缴金额和总金额
        BigDecimal paidAmount = bill.getPaidAmount() != null ? bill.getPaidAmount() : BigDecimal.ZERO;
        BigDecimal totalAmount = bill.getTotalAmount();

        // 如果已全额缴费，应该供热
        if (paidAmount.compareTo(totalAmount) >= 0) {
            return true;
        }

        // 如果有部分缴费，需要检查是否达到最低缴费要求
        // 这里可以根据业务规则调整，比如缴费比例达到一定程度就可以供热
        BigDecimal paymentRatio = paidAmount.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP);

        // 假设缴费比例达到30%就可以供热（可以根据实际业务调整）
        return paymentRatio.compareTo(new BigDecimal("0.3")) >= 0;
    }
}