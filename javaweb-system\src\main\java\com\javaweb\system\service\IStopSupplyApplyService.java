package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.StopSupplyApply;
import com.javaweb.system.query.StopSupplyApplyQuery;

/**
 * <p>
 * 申请停供表 服务类
 * </p>
 */
public interface IStopSupplyApplyService extends IService<StopSupplyApply> {

    /**
     * 获取申请停供数据列表
     * @param stopSupplyApplyQuery 查询条件
     * @return
     */
    JsonResult getList(StopSupplyApplyQuery stopSupplyApplyQuery);

    /**
     * 添加或编辑申请停供
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(StopSupplyApply entity);

    /**
     * 删除申请停供
     * @param ids ID数组
     * @return
     */
    JsonResult deleteByIds(Long[] ids);

    /**
     * 获取申请停供详情
     * @param id 申请ID
     * @return
     */
    JsonResult info(Long id);

    /**
     * 审核申请停供
     * @param id 申请ID
     * @param status 审核状态（approved=批准，rejected=拒绝）
     * @param reason 审核意见
     * @return
     */
    JsonResult approve(Long id, String status, String reason);

}
