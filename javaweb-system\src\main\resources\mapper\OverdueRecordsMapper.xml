<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.OverdueRecordsMapper">

    <!-- 获取欠费记录列表（带房屋信息） -->
    <select id="getOverdueRecordsList" resultType="com.javaweb.system.entity.OverdueRecords">
        SELECT 
            od.*,
            h.room_no,
            h.house_master as houseName,
            hu.name as heatUnitName,
            b.bill_no,
            b.due_date
        FROM t_overdue_records od
        LEFT JOIN t_house h ON od.house_id = h.id
        LEFT JOIN t_heat_unit hu ON h.heat_unit_id = hu.id
        LEFT JOIN t_bill b ON od.bill_id = b.id
        <where>
            <if test="query.heatUnitId != null and query.heatUnitId > 0">
                AND h.heat_unit_id = #{query.heatUnitId}
            </if>
            <if test="query.roomNo != null and query.roomNo != ''">
                AND h.room_no LIKE CONCAT('%', #{query.roomNo}, '%')
            </if>
            <if test="query.heatingYear != null">
                AND od.heating_year = #{query.heatingYear}
            </if>
            <if test="query.status != null and query.status.size() > 0">
                AND od.status IN
                <foreach collection="query.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.minOverdueAmount != null">
                AND od.overdue_amount >= #{query.minOverdueAmount}
            </if>
            <if test="query.maxOverdueAmount != null">
                AND od.overdue_amount &lt;= #{query.maxOverdueAmount}
            </if>
            <if test="query.minOverdueDays != null">
                AND od.overdue_days >= #{query.minOverdueDays}
            </if>
            <if test="query.maxOverdueDays != null">
                AND od.overdue_days &lt;= #{query.maxOverdueDays}
            </if>
            <if test="query.firstOverdueStartDate != null and query.firstOverdueStartDate != ''">
                AND od.first_overdue_date >= #{query.firstOverdueStartDate}
            </if>
            <if test="query.firstOverdueEndDate != null and query.firstOverdueEndDate != ''">
                AND od.first_overdue_date &lt;= #{query.firstOverdueEndDate}
            </if>
        </where>
        ORDER BY od.created_at DESC
    </select>

</mapper>
