package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatingApplication;
import com.javaweb.system.query.HeatingApplicationQuery;
import com.javaweb.system.service.IHeatingApplicationService;
import com.javaweb.system.dto.HeatingApplicationApproveDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用热申请 前端控制器
 * </p>
 */
@RestController
@RequestMapping("/heatingApplication")
public class HeatingApplicationController extends BaseController {

    @Autowired
    private IHeatingApplicationService heatingApplicationService;

    /**
     * 获取用热申请列表
     * @param heatingApplicationQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HeatingApplicationQuery heatingApplicationQuery) {
        return heatingApplicationService.getList(heatingApplicationQuery);
    }

    /**
     * 添加用热申请
     * @param entity 实体对象
     * @return
     */
    @Log(title = "用热申请", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HeatingApplication entity) {
        return heatingApplicationService.edit(entity);
    }

    /**
     * 修改用热申请
     * @param entity 实体对象
     * @return
     */
    @Log(title = "用热申请", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HeatingApplication entity) {
        return heatingApplicationService.edit(entity);
    }

    /**
     * 删除用热申请
     * @param ids ID数组
     * @return
     */
    @Log(title = "用热申请", logType = LogType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public JsonResult delete(@PathVariable("ids") Long[] ids) {
        return heatingApplicationService.deleteByIds(ids);
    }

    /**
     * 获取用热申请详情
     * @param id 申请ID
     * @return
     */
    @GetMapping("/info/{id}")
    public JsonResult info(@PathVariable("id") Long id) {
        return heatingApplicationService.info(id);
    }

    /**
     * 审核用热申请
     * @param approveDto 审批参数
     * @return
     */
    @Log(title = "审核用热申请", logType = LogType.UPDATE)
    @PostMapping("/approve")
    public JsonResult approve(@RequestBody HeatingApplicationApproveDto approveDto) {
        return heatingApplicationService.approve(
            approveDto.getId(),
            approveDto.getStatus(),
            approveDto.getReason(),
            approveDto.getRemark(),
            approveDto.getPaidAmount()
        );
    }

}
