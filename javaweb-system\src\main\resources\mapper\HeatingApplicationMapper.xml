<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HeatingApplicationMapper">

    <!-- 获取用热申请列表（带房屋信息） -->
    <select id="getHeatingApplicationList" resultType="com.javaweb.system.entity.HeatingApplication">
        SELECT 
            ha.*,
            h.room_no,
            h.house_master as houseName,
            hu.name as heatUnitName,
            u.realname as approverName
        FROM t_heating_application ha
        LEFT JOIN t_house h ON ha.house_id = h.id
        LEFT JOIN t_heat_unit hu ON h.heat_unit_id = hu.id
        LEFT JOIN t_user u ON ha.approved_by = u.id
        <where>
            <if test="query.heatUnitId != null and query.heatUnitId > 0">
                AND h.heat_unit_id = #{query.heatUnitId}
            </if>
            <if test="query.roomNo != null and query.roomNo != ''">
                AND h.room_no LIKE CONCAT('%', #{query.roomNo}, '%')
            </if>
            <if test="query.residentName != null and query.residentName != ''">
                AND ha.resident_name LIKE CONCAT('%', #{query.residentName}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND ha.phone LIKE CONCAT('%', #{query.phone}, '%')
            </if>
            <if test="query.applySeason != null and query.applySeason != ''">
                AND ha.apply_season = #{query.applySeason}
            </if>
            <if test="query.currentStatus != null and query.currentStatus.size() > 0">
                AND ha.current_status IN
                <foreach collection="query.currentStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.applyStartDate != null and query.applyStartDate != ''">
                AND ha.apply_time >= #{query.applyStartDate}
            </if>
            <if test="query.applyEndDate != null and query.applyEndDate != ''">
                AND ha.apply_time &lt;= #{query.applyEndDate}
            </if>
            <if test="query.approvedBy != null and query.approvedBy != ''">
                AND ha.approved_by = #{query.approvedBy}
            </if>
            <if test="query.source != null and query.source != ''">
                AND ha.source = #{query.source}
            </if>
        </where>
        ORDER BY ha.apply_time DESC
    </select>

</mapper>
