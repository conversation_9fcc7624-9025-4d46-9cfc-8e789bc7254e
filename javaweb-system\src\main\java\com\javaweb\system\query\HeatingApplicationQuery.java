package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 用热申请查询条件
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HeatingApplicationQuery extends BaseQuery {

    /**
     * 小区ID
     */
    private Long heatUnitId;

    /**
     * 房号
     */
    private String roomNo;

    /**
     * 申请人姓名
     */
    private String residentName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 申请供暖季
     */
    private String applySeason;

    /**
     * 申请状态
     */
    private List<String> currentStatus;

    /**
     * 申请开始日期
     */
    private String applyStartDate;

    /**
     * 申请结束日期
     */
    private String applyEndDate;

    /**
     * 审批人ID
     */
    private String approvedBy;

    /**
     * 申请来源
     */
    private String source;

}
