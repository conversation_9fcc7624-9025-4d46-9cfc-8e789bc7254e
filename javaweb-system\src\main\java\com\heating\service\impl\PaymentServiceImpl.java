package com.heating.service.impl;

import com.heating.entity.bill.TPayment;
import com.heating.entity.bill.TPayment.PaymentMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 缴费服务实现类（兼容性类）
 * </p>
 */
@Service
public class PaymentServiceImpl {

    /**
     * 提交支付
     * 
     * @param houseId 房屋ID
     * @param billId 账单ID
     * @param amount 支付金额
     * @param paymentMethodStr 支付方式字符串
     * @param transactionNo 交易流水号
     * @return 支付结果
     */
    @Transactional
    public boolean submitPayment(Integer houseId, Integer billId, BigDecimal amount, 
                               String paymentMethodStr, String transactionNo) {
        try {
            // 创建支付记录
            return createPaymentRecord(houseId, billId, amount, paymentMethodStr, transactionNo);
        } catch (Exception e) {
            System.err.println("提交支付失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建支付记录
     * 
     * @param houseId 房屋ID
     * @param billId 账单ID
     * @param amount 支付金额
     * @param paymentMethodStr 支付方式字符串
     * @param transactionNo 交易流水号
     * @return 创建结果
     */
    @Transactional
    public boolean createPaymentRecord(Integer houseId, Integer billId, BigDecimal amount, 
                                     String paymentMethodStr, String transactionNo) {
        try {
            // 将字符串转换为枚举
            PaymentMethod paymentMethod = convertStringToPaymentMethod(paymentMethodStr);
            
            if (paymentMethod == null) {
                System.err.println("不支持的支付方式: " + paymentMethodStr);
                return false;
            }

            // 创建支付记录对象
            TPayment payment = new TPayment();
            payment.setHouseId(houseId);
            payment.setBillId(billId);
            payment.setAmount(amount);
            payment.setPaymentMethod(paymentMethod);
            payment.setTransactionNo(transactionNo);
            payment.setPaymentDate(new Date());
            payment.setCreateTime(new Date());
            payment.setUpdateTime(new Date());
            payment.setMark(true);

            // 这里应该调用实际的数据库保存操作
            // 由于这是兼容性类，我们只是模拟保存成功
            System.out.println("创建支付记录成功: 房屋ID=" + houseId + 
                             ", 账单ID=" + billId + 
                             ", 金额=" + amount + 
                             ", 支付方式=" + paymentMethod.getName() + 
                             ", 交易流水号=" + transactionNo);
            
            return true;
            
        } catch (Exception e) {
            System.err.println("创建支付记录失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 将字符串转换为支付方式枚举
     * 
     * @param paymentMethodStr 支付方式字符串
     * @return 支付方式枚举
     */
    private PaymentMethod convertStringToPaymentMethod(String paymentMethodStr) {
        if (paymentMethodStr == null || paymentMethodStr.trim().isEmpty()) {
            return null;
        }

        // 转换为大写进行匹配
        String upperStr = paymentMethodStr.toUpperCase();
        
        try {
            // 首先尝试直接匹配枚举名称
            return PaymentMethod.valueOf(upperStr);
        } catch (IllegalArgumentException e) {
            // 如果直接匹配失败，尝试通过代码匹配
            return PaymentMethod.getByCode(paymentMethodStr.toLowerCase());
        }
    }

    /**
     * 验证支付方式是否有效
     * 
     * @param paymentMethodStr 支付方式字符串
     * @return 是否有效
     */
    public boolean isValidPaymentMethod(String paymentMethodStr) {
        return convertStringToPaymentMethod(paymentMethodStr) != null;
    }

    /**
     * 获取所有支持的支付方式
     * 
     * @return 支付方式数组
     */
    public PaymentMethod[] getSupportedPaymentMethods() {
        return PaymentMethod.values();
    }

    /**
     * 根据支付方式代码获取支付方式名称
     * 
     * @param code 支付方式代码
     * @return 支付方式名称
     */
    public String getPaymentMethodName(String code) {
        PaymentMethod method = PaymentMethod.getByCode(code);
        return method != null ? method.getName() : "未知支付方式";
    }
}
