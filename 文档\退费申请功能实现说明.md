# 退费申请功能实现说明

## 功能概述

已完整实现`createRefundApplication`方法，当停供申请审批通过且用户已全额缴费时，系统会自动在`t_refund`表中创建退费申请记录。

## 实现详情

### 1. 依赖注入添加

在`StopSupplyApplyServiceImpl`中添加了必要的依赖：

```java
@Autowired
private IRefundService refundService; // 退费服务

import com.javaweb.system.entity.Refund; // 退费实体类
import com.javaweb.system.service.IRefundService; // 退费服务接口
```

### 2. createRefundApplication方法完整实现

```java
/**
 * 创建退费申请
 * 在t_refund表中创建一条新的退费申请记录
 * 
 * @param bill 账单信息
 * @param refundAmount 退费金额
 */
private void createRefundApplication(Bill bill, BigDecimal refundAmount) {
    try {
        // 创建退费申请记录
        Refund refund = new Refund();
        
        // 设置基本信息
        refund.setHouseId(bill.getHouseId()); // 关联房屋ID
        refund.setBillId(bill.getId()); // 关联账单ID
        refund.setRefundAmount(refundAmount); // 退费金额
        refund.setRefundReason("用户中途申请停供，退还多缴费用"); // 退费原因
        
        // 设置时间信息
        Date currentTime = new Date();
        refund.setCreatedAt(currentTime); // 创建时间
        refund.setUpdatedAt(currentTime); // 更新时间
        
        // 保存退费申请记录
        boolean result = refundService.save(refund);
        
        if (result) {
            System.out.println("成功创建退费申请：账单ID=" + bill.getId() + 
                              "，退费金额=" + refundAmount + "元" +
                              "，退费申请ID=" + refund.getId());
        } else {
            System.err.println("创建退费申请失败：账单ID=" + bill.getId() + 
                              "，退费金额=" + refundAmount + "元");
        }
        
    } catch (Exception e) {
        System.err.println("创建退费申请异常：账单ID=" + bill.getId() + 
                          "，退费金额=" + refundAmount + "元" +
                          "，异常信息=" + e.getMessage());
        // 不抛出异常，避免影响主流程
    }
}
```

## 数据库字段映射

### t_refund表字段对应关系

| 字段名 | 数据类型 | 说明 | 设置值 |
|--------|----------|------|--------|
| id | int(11) | 主键ID | 自动生成 |
| house_id | int(11) | 关联房屋ID | bill.getHouseId() |
| bill_id | int(11) | 关联账单ID | bill.getId() |
| refund_amount | decimal(10,2) | 退费金额 | 计算得出的退费金额 |
| refund_reason | varchar(255) | 退费原因 | "用户中途申请停供，退还多缴费用" |
| created_at | datetime | 创建时间 | 当前时间 |
| updated_at | datetime | 更新时间 | 当前时间 |

## 业务流程

### 触发条件
1. 停供申请审批通过（status = "approved"）
2. 申请日期在供暖开始后
3. 用户已全额缴费（paid_amount >= total_amount）
4. 计算出的应退金额 > 0

### 执行流程
1. **计算应退金额**：
   ```java
   BigDecimal refundAmount = paidAmount.subtract(settlementAmount);
   ```

2. **调用退费申请创建**：
   ```java
   if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
       createRefundApplication(bill, refundAmount);
   }
   ```

3. **创建退费记录**：
   - 设置关联信息（房屋ID、账单ID）
   - 设置退费金额和原因
   - 保存到数据库

4. **更新账单备注**：
   ```java
   String newRemark = currentRemark + "\n用户已申请停供，已发起退费申请，金额" + refundAmount + "元";
   bill.setRemark(newRemark);
   ```

## 计算示例

### 场景：用户已全额缴费后申请停供

**假设条件**：
- 账单总金额：2500元
- 已缴金额：2500元
- 供暖期：2024-11-15 至 2025-03-15（120天）
- 停供生效日：2024-12-15（供暖30天后）
- 最低缴费比例：30%

**计算过程**：
1. 实际供暖费用 = 2500 × (30/120) = 625元
2. 基础热损费 = 2500 × 0.3 = 750元
3. 最终应收金额 = MAX(625, 750) = 750元
4. 应退金额 = 2500 - 750 = 1750元

**退费记录**：
```json
{
  "house_id": 123,
  "bill_id": 456,
  "refund_amount": 1750.00,
  "refund_reason": "用户中途申请停供，退还多缴费用",
  "created_at": "2024-12-15 10:30:00",
  "updated_at": "2024-12-15 10:30:00"
}
```

## 异常处理

### 1. 数据库操作异常
- 捕获保存失败的异常
- 记录详细的错误日志
- 不影响主流程继续执行

### 2. 参数验证
- 验证退费金额必须大于0
- 验证账单和房屋信息存在

### 3. 日志记录
- **成功日志**：记录退费申请ID和金额
- **失败日志**：记录失败原因和相关信息
- **异常日志**：记录异常详情便于排查

## 后续处理流程

### 1. 退费审核
创建的退费申请需要管理员审核：
- 状态：pending（待审核）
- 管理员可以通过退费管理界面进行审核
- 审核通过后执行实际退费操作

### 2. 退费执行
- 审核通过后更新账单的已缴金额
- 记录退费操作日志
- 通知用户退费结果

### 3. 状态跟踪
- pending：待审核
- approved：已批准
- rejected：已拒绝
- completed：已完成

## 技术特点

### 1. 事务安全
- 退费申请创建在停供审批的同一事务中
- 任何步骤失败都会触发回滚

### 2. 异常隔离
- 退费申请创建失败不影响停供审批流程
- 独立的异常处理和日志记录

### 3. 数据完整性
- 完整的关联关系（房屋、账单）
- 准确的金额计算和原因说明

### 4. 可追溯性
- 详细的创建时间记录
- 完整的操作日志
- 明确的退费原因说明

## 注意事项

### 1. 数据库字段兼容性
- 确保t_refund表结构与Refund实体类匹配
- 注意字段名称和数据类型的一致性

### 2. 金额精度
- 使用BigDecimal确保金额计算精度
- 保留两位小数

### 3. 业务规则
- 只有已全额缴费的用户才会创建退费申请
- 退费金额必须大于0才创建记录

### 4. 性能考虑
- 异步处理退费申请创建
- 避免阻塞主要业务流程

这个完整的退费申请功能实现确保了停供审批流程的完整性，为已缴费用户提供了自动化的退费申请服务，提高了系统的智能化程度和用户体验。
