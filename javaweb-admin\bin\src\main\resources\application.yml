spring:
  profiles:
    active: '@package.environment@'

  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false

  # 自定义国际化配置
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
    encoding: UTF-8
    
  # 日志配置
  logging:
    level:
      root: ERROR                   # 全局日志级别
      com.javaweb: ERROR            # 应用日志级别
      org.springframework: ERROR    # Spring框架日志级别
      org.mybatis: ERROR            # MyBatis日志级别
      com.baomidou: ERROR           # MyBatisPlus日志级别
      org.apache: ERROR             # Apache相关库日志级别
      com.alibaba: ERROR            # 阿里相关库日志级别
      druid.sql: ERROR              # Druid SQL日志级别
    file:
      name: logs/javaweb-admin.log  # 日志文件位置
    pattern:
      console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"  # 控制台日志格式
      file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"     # 文件日志格式

# MyBatis
mybatis-plus:
  mapper-locations: classpath*:mapper/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.javaweb.**.mapper
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
