# 申请停供审批功能说明

## 功能概述

申请停供审批功能已经增强，当审批同意停供申请时，系统会自动更新相关的账单和房屋供热状态。

## 审批流程

### 1. 审批拒绝
- 只更新申请状态为"rejected"
- 不影响账单和房屋状态

### 2. 审批同意
当审批同意停供申请时，系统会执行以下操作：

#### 2.1 更新申请状态
- 设置申请状态为"approved"
- 记录审批人和审批时间
- 添加审批意见

#### 2.2 更新账单信息（如果账单存在）
根据申请日期和供热规则进行不同处理：

**申请日期在供热开始前**：
- **应缴费金额**：设置为原账单金额 × 最低缴费比例
- **供热状态**：设置为不供热（false）
- **备注信息**：添加"停供申请审批通过（供热前申请），按最低缴费比例收费"

**申请日期在供热开始后**：
- **应缴费金额**：设置为原账单金额 × 最低缴费比例
- **供热状态**：设置为不供热（false）
- **备注信息**：添加"停供申请审批通过（供热后申请），按最低缴费比例收费"

**申请日期超过逾期日**：
- 除了更新账单外，还需要创建或更新欠费记录
- **欠费金额**：最低缴费金额 + 滞纳金
- **滞纳金计算**：原账单金额 × 滞纳金日利率 × 逾期天数

#### 2.3 更新房屋信息
- **供热状态**：设置为不供热（isHeating = 0）

## 技术实现

### 核心方法

#### approve方法
```java
@Transactional(rollbackFor = Exception.class)
public JsonResult approve(Long id, String status, String reason)
```

#### updateBillAndHouseStatus方法
```java
private void updateBillAndHouseStatus(StopSupplyApply stopSupplyApply)
```

#### handleOverdueRecord方法
```java
private void handleOverdueRecord(Bill bill, HeatingFeeRule activeRule, LocalDate applyLocalDate,
                               LocalDate overdueDate, BigDecimal originalAmount, BigDecimal minPaymentAmount)
```

#### findBillByHouseIdAndYear方法
```java
private Bill findBillByHouseIdAndYear(Long houseId, Integer heatingYear)
```

### 事务管理
- 使用`@Transactional`注解确保数据一致性
- 如果更新账单或房屋状态失败，整个审批操作会回滚

### 异常处理
- 捕获更新过程中的异常
- 抛出RuntimeException触发事务回滚
- 保证数据的完整性

## 业务逻辑

### 查找账单逻辑
- 根据房屋ID和供暖年度查找对应账单
- 如果找到多个账单，取第一个
- 如果没有找到账单，跳过账单更新

### 状态更新逻辑
1. **账单更新**：
   - 根据申请日期计算应缴费金额（原金额 × 最低缴费比例）
   - 供热状态设为false（不供热）
   - 添加相应的备注说明

2. **房屋更新**：
   - 供热状态设为0（不供热）

3. **欠费记录处理**（仅当申请日期超过逾期日时）：
   - 计算逾期天数和滞纳金
   - 创建或更新欠费记录
   - 欠费金额 = 最低缴费金额 + 滞纳金

## 数据库影响

### t_bill表更新字段
- `total_amount` = 原金额 × 最低缴费比例
- `is_heating` = false
- `remark` = 相应的备注信息

### t_house表更新字段
- `is_heating` = 0

### t_overdue_records表（仅当超过逾期日时）
- `bill_id` = 账单ID
- `house_id` = 房屋ID
- `heating_year` = 供暖年度
- `total_amount` = 原账单金额
- `paid_amount` = 0.00
- `overdue_amount` = 最低缴费金额 + 滞纳金
- `overdue_days` = 逾期天数
- `penalty_rate` = 滞纳金日利率
- `penalty_amount` = 滞纳金金额
- `status` = 'active'

## 使用场景

### 适用情况
- 用户申请停供且审批同意
- 需要自动调整账单和供热状态
- 避免手动修改多个表的数据

### 注意事项
1. 只有审批同意时才会更新账单和房屋状态
2. 如果账单不存在，只更新房屋状态
3. 所有操作在同一事务中执行，保证数据一致性
4. 更新失败会触发事务回滚
5. 根据申请日期与供热开始日期的关系进行不同处理
6. 超过逾期日的申请会自动创建欠费记录
7. 滞纳金计算基于原账单金额，而非最低缴费金额

## 测试建议

### 测试用例
1. **供热前申请审批同意**：验证账单按最低缴费比例收费，无欠费记录
2. **供热后未逾期申请审批同意**：验证账单按最低缴费比例收费，无欠费记录
3. **供热后已逾期申请审批同意**：验证账单按最低缴费比例收费，创建欠费记录
4. **审批拒绝**：验证只更新申请状态，不影响其他数据
5. **账单不存在**：验证只更新房屋状态
6. **更新失败**：验证事务回滚机制
7. **重复审批**：验证防重复审批逻辑

### 验证点
- 申请状态是否正确更新
- 账单金额是否按最低缴费比例计算
- 账单供热状态是否设为false
- 房屋供热状态是否设为0
- 逾期情况下是否正确创建欠费记录
- 滞纳金计算是否正确
- 事务回滚是否正常工作
