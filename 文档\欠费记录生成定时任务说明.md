# 欠费记录生成定时任务说明

## 功能概述

在WorkThreadServer.java中实现了欠费记录生成的定时任务，每天凌晨00:30自动执行，用于扫描逾期账单并生成或更新欠费记录。

## 定时任务配置

### 执行时间
```java
@Scheduled(cron = "0 30 0 * * ?") // 每天凌晨00:30执行
```

### 任务名称
`generateOverdueRecordsTask()` - 欠费记录生成定时任务

## 业务逻辑

### 1. 触发条件
系统会扫描满足以下条件的账单：
- **due_date（缴费截止日）已过**：账单的缴费截止日期小于当前日期
- **paid_amount < total_amount**：已缴金额小于应缴总金额

### 2. 处理流程

#### 步骤1：获取生效的供暖计费规则
```java
HeatingFeeRule activeRule = getActiveHeatingFeeRule();
```
- 从`t_heating_fee_rule`表查询`is_active = true`的记录
- 获取滞纳金日利率等关键参数

#### 步骤2：查询逾期账单
```java
List<Bill> overdueBills = getOverdueBills(currentDate);
```
- 查询条件：`due_date < 当前日期 AND paid_amount < total_amount`
- 返回所有符合条件的逾期账单

#### 步骤3：处理每个逾期账单
对每个逾期账单执行以下逻辑：

**检查是否已有欠费记录**：
```java
QueryWrapper<OverdueRecords> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("bill_id", bill.getId());
OverdueRecords existingRecord = overdueRecordsService.getOne(queryWrapper);
```

**情况A：不存在欠费记录**
- 调用`createNewOverdueRecord()`创建新记录
- 设置所有必要字段

**情况B：已存在欠费记录**
- 调用`updateExistingOverdueRecord()`更新现有记录
- 重新计算逾期天数和滞纳金

### 3. 关键字段计算

#### 新建欠费记录时的字段赋值

**基础信息**：
- `bill_id`：关联原账单ID
- `house_id`：关联房屋ID
- `heating_year`：供暖年度

**金额计算**：
- `total_amount`：账单总金额
- `paid_amount`：已缴金额
- `overdue_amount`：欠费金额 = total_amount - paid_amount

**逾期计算**：
- `overdue_days`：逾期天数 = 当前日期 - due_date
- `first_overdue_date`：首次逾期日期 = due_date + 1天

**滞纳金计算**：
- `penalty_rate`：从供暖规则表获取
- `penalty_amount`：滞纳金 = overdue_amount × penalty_rate × overdue_days

**状态设置**：
- `status`：'active'（生效中）
- `last_updated_date`：当前日期

#### 更新现有记录时的逻辑

**重新计算逾期天数**：
```java
long newOverdueDays = ChronoUnit.DAYS.between(dueDate, today);
```

**重新计算滞纳金**：
```java
BigDecimal newPenaltyAmount = newOverdueAmount
    .multiply(activeRule.getPenaltyRate())
    .multiply(BigDecimal.valueOf(newOverdueDays));
```

**更新字段**：
- `overdue_days`：新的逾期天数
- `penalty_amount`：重新计算的滞纳金
- `last_updated_date`：当前日期

**状态判断**：
- 如果欠费金额 ≤ 0，设置状态为'cleared'（已结清）

## 技术实现

### 依赖注入
```java
@Autowired
private IOverdueRecordsService overdueRecordsService; // 欠费记录服务

@Autowired
private BillMapper billMapper; // 账单数据访问对象

@Autowired
private HeatingFeeRuleMapper heatingFeeRuleMapper; // 供暖计费规则数据访问对象
```

### 核心方法

#### 1. getActiveHeatingFeeRule()
- 获取当前生效的供暖计费规则
- 查询条件：`is_active = true`

#### 2. getOverdueBills(Date currentDate)
- 查询所有逾期且未完全缴费的账单
- 查询条件：`due_date < currentDate AND paid_amount < total_amount`

#### 3. createNewOverdueRecord()
- 创建新的欠费记录
- 计算所有必要字段并保存

#### 4. updateExistingOverdueRecord()
- 更新已存在的欠费记录
- 重新计算逾期天数和滞纳金

### 异常处理
- 每个账单的处理都有独立的异常捕获
- 单个账单处理失败不影响其他账单
- 详细的错误日志记录

### 日志记录
- 任务开始和结束的日志
- 处理结果统计（新创建、更新的记录数）
- 详细的调试日志（可选）
- 异常情况的错误日志

## 计算示例

### 示例1：新建欠费记录
```
账单信息：
- 账单ID：123
- 应缴金额：2500元
- 已缴金额：0元
- 缴费截止日：2024-10-31
- 当前日期：2024-11-01

计算结果：
- 欠费金额：2500 - 0 = 2500元
- 逾期天数：2024-11-01 - 2024-10-31 = 1天
- 首次逾期日期：2024-10-31 + 1 = 2024-11-01
- 滞纳金：2500 × 0.0005 × 1 = 1.25元
```

### 示例2：更新欠费记录
```
原记录：
- 逾期天数：1天
- 滞纳金：1.25元

今天更新：
- 新逾期天数：2天
- 新滞纳金：2500 × 0.0005 × 2 = 2.50元
```

## 注意事项

### 1. 数据一致性
- 所有操作都有事务保护
- 异常情况下不会产生脏数据

### 2. 性能考虑
- 批量查询逾期账单
- 单条记录处理，避免大事务

### 3. 业务规则
- 只处理真正逾期的账单
- 自动识别已结清的情况
- 滞纳金计算精确到分

### 4. 监控建议
- 监控任务执行时间
- 监控处理的记录数量
- 关注异常日志

## 扩展功能

### 1. 通知功能
可以在生成欠费记录后发送通知：
- 短信通知
- 邮件通知
- 系统内消息

### 2. 报表统计
可以基于欠费记录生成：
- 欠费统计报表
- 滞纳金收入统计
- 逾期趋势分析

### 3. 自动催缴
可以根据逾期天数自动触发：
- 催缴通知
- 停供预警
- 法律程序启动

这个定时任务确保了欠费记录的及时生成和准确计算，为后续的催缴、停供等业务流程提供了可靠的数据基础。
