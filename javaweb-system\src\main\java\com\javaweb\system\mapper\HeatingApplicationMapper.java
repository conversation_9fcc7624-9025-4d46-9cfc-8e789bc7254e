package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.system.entity.HeatingApplication;
import com.javaweb.system.query.HeatingApplicationQuery;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用热申请表 Mapper 接口
 * </p>
 */
public interface HeatingApplicationMapper extends BaseMapper<HeatingApplication> {

    /**
     * 获取用热申请列表（带房屋信息）
     * @param page 分页对象
     * @param query 查询条件
     * @return
     */
    IPage<HeatingApplication> getHeatingApplicationList(Page<HeatingApplication> page, @Param("query") HeatingApplicationQuery query);

}
