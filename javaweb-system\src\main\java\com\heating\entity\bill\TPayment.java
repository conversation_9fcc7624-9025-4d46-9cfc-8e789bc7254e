package com.heating.entity.bill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 缴费记录表（兼容性类）
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_payment")
public class TPayment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        /**
         * 微信支付
         */
        WECHAT("wechat", "微信支付"),
        
        /**
         * 支付宝支付
         */
        ALIPAY("alipay", "支付宝支付"),
        
        /**
         * 银行转账
         */
        BANK_TRANSFER("bank_transfer", "银行转账"),
        
        /**
         * 现金支付
         */
        CASH("cash", "现金支付");
        
        /**
         * 支付方式代码
         */
        private final String code;
        
        /**
         * 支付方式名称
         */
        private final String name;
        
        /**
         * 构造函数
         * 
         * @param code 支付方式代码
         * @param name 支付方式名称
         */
        PaymentMethod(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        /**
         * 获取支付方式代码
         * 
         * @return 支付方式代码
         */
        public String getCode() {
            return code;
        }
        
        /**
         * 获取支付方式名称
         * 
         * @return 支付方式名称
         */
        public String getName() {
            return name;
        }
        
        /**
         * 根据代码获取支付方式枚举
         * 
         * @param code 支付方式代码
         * @return 支付方式枚举，如果找不到则返回null
         */
        public static PaymentMethod getByCode(String code) {
            if (code == null) {
                return null;
            }
            
            for (PaymentMethod method : PaymentMethod.values()) {
                if (method.getCode().equals(code)) {
                    return method;
                }
            }
            return null;
        }
        
        @Override
        public String toString() {
            return this.code;
        }
    }

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联房屋ID
     */
    private Integer houseId;

    /**
     * 关联账单ID
     */
    private Integer billId;

    /**
     * 支付方式
     */
    private PaymentMethod paymentMethod;

    /**
     * 供暖年度（如2024）
     */
    private Integer heatYear;

    /**
     * 缴费金额（元）
     */
    private BigDecimal amount;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 缴费日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;

    /**
     * 备注（如说明分次缴费）
     */
    private String remark;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private Integer updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 有效标识
     */
    private Boolean mark;

    /**
     * 非数据库字段
     */
    @TableField(exist = false)
    private String heatUnitName;

    @TableField(exist = false)
    private String heatUnitFloorName;

    @TableField(exist = false)
    private String heatUnitFloorUnitName;

    @TableField(exist = false)
    private String roomNo;

    @TableField(exist = false)
    private String houseName;

    @TableField(exist = false)
    private String billInfo;

    @TableField(exist = false)
    private Integer heatUnitId;

    @TableField(exist = false)
    private Integer heatUnitFloorId;

    @TableField(exist = false)
    private Integer heatUnitFloorUnitId;

    /**
     * 供暖状态
     */
    @TableField(exist = false)
    private Boolean isHeating;
}
