package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.StopSupplyApply;
import com.javaweb.system.entity.Bill;
import com.javaweb.system.entity.HeatingFeeRule;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.OverdueRecords;
import com.javaweb.system.entity.Refund;
import com.javaweb.system.mapper.HeatingFeeRuleMapper;
import com.javaweb.system.mapper.StopSupplyApplyMapper;
import com.javaweb.system.service.IBillService;
import com.javaweb.system.service.IHouseInfoService;
import com.javaweb.system.service.IOverdueRecordsService;
import com.javaweb.system.service.IRefundService;
import com.javaweb.system.query.StopSupplyApplyQuery;
import com.javaweb.system.service.IStopSupplyApplyService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 申请停供表 服务实现类
 * </p>
 */
@Service
public class StopSupplyApplyServiceImpl extends ServiceImpl<StopSupplyApplyMapper, StopSupplyApply> implements IStopSupplyApplyService {

    @Autowired
    private StopSupplyApplyMapper stopSupplyApplyMapper;

    @Autowired
    private IBillService billService;

    @Autowired
    private IHouseInfoService houseInfoService;

    @Autowired
    private IOverdueRecordsService overdueRecordsService;

    @Autowired
    private IRefundService refundService;

    @Autowired
    private HeatingFeeRuleMapper heatingFeeRuleMapper;

    /**
     * 获取申请停供数据列表
     */
    @Override
    public JsonResult getList(StopSupplyApplyQuery stopSupplyApplyQuery) {
        // 分页查询
        Page<StopSupplyApply> page = new Page<>(stopSupplyApplyQuery.getPage(), stopSupplyApplyQuery.getLimit());
        IPage<StopSupplyApply> data = stopSupplyApplyMapper.getStopSupplyApplyList(page, stopSupplyApplyQuery);

        return JsonResult.success(data, "查询成功");
    }

    /**
     * 添加或编辑申请停供
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult edit(StopSupplyApply entity) {
        if (entity.getId() != null && entity.getId() > 0) {
            // 编辑
            entity.setUpdatedAt(new Date());
            
            // 检查申请是否已审批，已审批的不能修改
            StopSupplyApply existApply = getById(entity.getId());
            if (existApply != null && !"pending".equals(existApply.getStatus())) {
                return JsonResult.error("该申请已审批，不能修改");
            }
        } else {
            // 新增
            entity.setApplyDate(new Date());
            entity.setStatus("pending");
            entity.setCreatedAt(new Date());
            entity.setUpdatedAt(new Date());
            
            // 检查是否已存在同一房屋同一年度的待审批申请
            QueryWrapper<StopSupplyApply> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("house_id", entity.getHouseId());
            queryWrapper.eq("heating_year", entity.getHeatingYear());
            queryWrapper.eq("status", "pending");
            int count = count(queryWrapper);
            if (count > 0) {
                return JsonResult.error("该房屋已存在" + entity.getHeatingYear() + "年度的待审批停供申请");
            }
        }
        
        boolean result = saveOrUpdate(entity);
        if (result) {
            return JsonResult.success("保存成功");
        } else {
            return JsonResult.error("保存失败");
        }
    }

    /**
     * 删除申请停供
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteByIds(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        
        // 检查是否有已审批的申请，已审批的不能删除
        QueryWrapper<StopSupplyApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        queryWrapper.ne("status", "pending");
        int count = count(queryWrapper);
        if (count > 0) {
            return JsonResult.error("存在已审批的申请，不能删除");
        }
        
        boolean result = removeByIds(idList);
        if (result) {
            return JsonResult.success("删除成功");
        } else {
            return JsonResult.error("删除失败");
        }
    }

    /**
     * 获取申请停供详情
     */
    @Override
    public JsonResult info(Long id) {
        StopSupplyApply entity = getById(id);
        return JsonResult.success(entity, "查询成功");
    }

    /**
     * 审核申请停供
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult approve(Long id, String status, String reason) {
        StopSupplyApply entity = getById(id);
        if (entity == null) {
            return JsonResult.error("申请不存在");
        }

        if (!"pending".equals(entity.getStatus())) {
            return JsonResult.error("该申请已审批，不能重复审批");
        }

        if (!"approved".equals(status) && !"rejected".equals(status)) {
            return JsonResult.error("审批状态参数错误");
        }

        // 更新申请状态
        entity.setStatus(status);
        entity.setApprovedBy(ShiroUtils.getUserId().longValue());
        entity.setApprovedAt(new Date());
        entity.setUpdatedAt(new Date());
        if (StringUtils.isNotEmpty(reason)) {
            entity.setReason(entity.getReason() + "\n审批意见：" + reason);
        }

        boolean result = updateById(entity);
        if (!result) {
            return JsonResult.error("审批失败");
        }

        // 如果审批同意，需要更新账单和房屋供热状态
        if ("approved".equals(status)) {
            try {
                updateBillAndHouseStatus(entity);
            } catch (Exception e) {
                // 如果更新失败，抛出异常回滚事务
                throw new RuntimeException("更新账单和房屋状态失败：" + e.getMessage(), e);
            }
        }

        return JsonResult.success("审批成功");
    }

    /**
     * 管理员审批通过后的业务逻辑处理
     * 根据停供申请的时间和缴费状态，执行不同的业务处理流程
     *
     * @param stopSupplyApply 停供申请信息
     */
    private void updateBillAndHouseStatus(StopSupplyApply stopSupplyApply) {
        Long houseId = stopSupplyApply.getHouseId();
        Integer heatingYear = stopSupplyApply.getHeatingYear();
        Date applyDate = stopSupplyApply.getApplyDate();
        Date effectiveDate = applyDate; // 停供生效日期（使用申请日期作为生效日期）

        // 1. 获取当前生效的供暖计费规则
        QueryWrapper<HeatingFeeRule> ruleQuery = new QueryWrapper<>();
        ruleQuery.eq("is_active", true);
        HeatingFeeRule activeRule = heatingFeeRuleMapper.selectOne(ruleQuery);

        if (activeRule == null) {
            throw new RuntimeException("未找到生效的供暖计费规则");
        }

        // 2. 查找对应的账单
        Bill bill = findBillByHouseIdAndYear(houseId, heatingYear);
        if (bill == null) {
            throw new RuntimeException("未找到对应的供暖账单");
        }

        // 3. 获取供暖开始日期和结束日期
        Date heatingStartDate = activeRule.getHeatingStartDate();
        Date heatingEndDate = activeRule.getHeatingEndDate();

        // 4. 根据停供生效日期与供暖开始日期的关系，判断处理场景
        if (effectiveDate.before(heatingStartDate)) {
            // 场景1：在供暖开始前申请并获批停供
            handlePreHeatingStopSupply(bill, activeRule, stopSupplyApply);
        } else {
            // 场景2和3：在供暖开始后申请停供
            handlePostHeatingStopSupply(bill, activeRule, stopSupplyApply, heatingStartDate, heatingEndDate);
        }

        // 5. 更新房屋供热状态为不供热
        updateHouseHeatingStatus(houseId);
    }

    /**
     * 场景1：在供暖开始前申请并获批停供
     * 直接修改审批状态即可，无需复杂的财务计算
     *
     * @param bill 账单信息
     * @param activeRule 供暖计费规则
     * @param stopSupplyApply 停供申请
     */
    private void handlePreHeatingStopSupply(Bill bill, HeatingFeeRule activeRule, StopSupplyApply stopSupplyApply) {
        // 供暖开始前申请停供，直接修改审批状态即可
        // 账单保持原状，房屋状态已在主方法中更新
        System.out.println("处理供暖开始前的停供申请，账单ID: " + bill.getId());
    }

    /**
     * 场景2和3：在供暖开始后申请停供
     * 需要根据缴费状态进行不同的处理
     *
     * @param bill 账单信息
     * @param activeRule 供暖计费规则
     * @param stopSupplyApply 停供申请
     * @param heatingStartDate 供暖开始日期
     * @param heatingEndDate 供暖结束日期
     */
    private void handlePostHeatingStopSupply(Bill bill, HeatingFeeRule activeRule, StopSupplyApply stopSupplyApply,
                                           Date heatingStartDate, Date heatingEndDate) {
        Date effectiveDate = stopSupplyApply.getApplyDate(); // 停供生效日期
        BigDecimal totalAmount = bill.getTotalAmount(); // 账单总金额
        BigDecimal paidAmount = bill.getPaidAmount() != null ? bill.getPaidAmount() : BigDecimal.ZERO; // 已缴金额

        // 计算结算金额
        BigDecimal settlementAmount = calculateSettlementAmount(totalAmount, effectiveDate, heatingStartDate,
                                                              heatingEndDate, activeRule);

        if (paidAmount.compareTo(BigDecimal.ZERO) == 0) {
            // 场景2：供暖开始后申请停供，且此前未缴费
            handlePostHeatingUnpaidStopSupply(bill, activeRule, settlementAmount);
        } else if (paidAmount.compareTo(totalAmount) >= 0) {
            // 场景3：供暖开始后申请停供，但此前已全额缴费
            handlePostHeatingPaidStopSupply(bill, activeRule, settlementAmount, paidAmount);
        } else {
            // 部分缴费情况（文档中未明确说明，按未缴费处理）
            handlePostHeatingUnpaidStopSupply(bill, activeRule, settlementAmount);
        }
    }

    /**
     * 计算停供结算金额
     * 根据实际供暖天数和最低缴费比例，取较大值作为结算金额
     *
     * @param totalAmount 账单总金额
     * @param effectiveDate 停供生效日期
     * @param heatingStartDate 供暖开始日期
     * @param heatingEndDate 供暖结束日期
     * @param activeRule 供暖计费规则
     * @return 结算金额
     */
    private BigDecimal calculateSettlementAmount(BigDecimal totalAmount, Date effectiveDate,
                                               Date heatingStartDate, Date heatingEndDate, HeatingFeeRule activeRule) {
        // 计算A：按实际供暖天数计算费用
        long totalDays = ChronoUnit.DAYS.between(
            heatingStartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
            heatingEndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        );

        long actualDays = ChronoUnit.DAYS.between(
            heatingStartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
            effectiveDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
        );

        // 确保实际天数不超过总天数，不小于0
        actualDays = Math.max(0, Math.min(actualDays, totalDays));

        BigDecimal actualHeatingFee = totalAmount
            .multiply(BigDecimal.valueOf(actualDays))
            .divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP);

        // 计算B：按最低缴费比例计算基础热损费
        BigDecimal baseHeatLossFee = totalAmount.multiply(activeRule.getMinPaymentRate());

        // 最终结算金额 = MAX(计算A, 计算B)
        BigDecimal settlementAmount = actualHeatingFee.max(baseHeatLossFee);

        System.out.println("结算金额计算：总金额=" + totalAmount + "，实际天数=" + actualDays +
                          "，总天数=" + totalDays + "，实际供暖费=" + actualHeatingFee +
                          "，基础热损费=" + baseHeatLossFee + "，最终结算金额=" + settlementAmount);

        return settlementAmount;
    }

    /**
     * 处理供暖开始后申请停供且此前未缴费的情况（场景2）
     *
     * @param bill 账单信息
     * @param activeRule 供暖计费规则
     * @param settlementAmount 结算金额
     */
    private void handlePostHeatingUnpaidStopSupply(Bill bill, HeatingFeeRule activeRule, BigDecimal settlementAmount) {
        // 1. 处理欠费记录表（t_overdue_records）
        handleOverdueRecordsForUnpaid(bill, settlementAmount);

        // 2. 更新账单表（t_bill）
        updateBillForUnpaidStopSupply(bill, settlementAmount);
    }

    /**
     * 处理供暖开始后申请停供且此前已全额缴费的情况（场景3）
     *
     * @param bill 账单信息
     * @param activeRule 供暖计费规则
     * @param settlementAmount 结算金额
     * @param paidAmount 已缴金额
     */
    private void handlePostHeatingPaidStopSupply(Bill bill, HeatingFeeRule activeRule,
                                               BigDecimal settlementAmount, BigDecimal paidAmount) {
        // 计算应退金额：已缴金额 - 最终应收金额
        BigDecimal refundAmount = paidAmount.subtract(settlementAmount);

        if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 1. 创建退费申请（这里暂时只更新账单备注，实际退费功能需要单独实现）
            createRefundApplication(bill, refundAmount);

            // 2. 更新账单表（t_bill）
            updateBillForPaidStopSupply(bill, refundAmount);
        }
    }

    /**
     * 处理未缴费情况下的欠费记录
     * 将现有欠费记录状态更新为已核销
     *
     * @param bill 账单信息
     * @param settlementAmount 结算金额
     */
    private void handleOverdueRecordsForUnpaid(Bill bill, BigDecimal settlementAmount) {
        // 查询与该账单关联的欠费记录
        QueryWrapper<OverdueRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_id", bill.getId());
        OverdueRecords overdueRecord = overdueRecordsService.getOne(queryWrapper);

        if (overdueRecord != null) {
            // 将状态从 active 更新为 written_off（已核销）
            overdueRecord.setStatus("written_off");
            // 注意：如果OverdueRecords实体没有remark字段，可以考虑添加到其他字段或日志中
            overdueRecord.setRemark("因中途申请停供获批，此欠费记录已核销，待用户缴纳结算款");
            overdueRecord.setUpdatedAt(new Date());
            overdueRecordsService.updateById(overdueRecord);

            System.out.println("已核销欠费记录，账单ID: " + bill.getId() + "，结算金额: " + settlementAmount);
        }
    }

    /**
     * 更新未缴费停供情况下的账单信息
     *
     * @param bill 账单信息
     * @param settlementAmount 结算金额
     */
    private void updateBillForUnpaidStopSupply(Bill bill, BigDecimal settlementAmount) {
        // 绝不修改 total_amount，保持 status 为 overdue
        String currentRemark = bill.getRemark() != null ? bill.getRemark() : "";
        String newRemark = currentRemark + "\n已生成停供结算，实际应收金额：" + settlementAmount + "元，滞纳金已免除";

        bill.setRemark(newRemark);
        bill.setIsHeating(false); // 设置为不供热
        // bill.setUpdatedAt(new Date()); // 如果Bill实体没有此字段，可以注释掉
        billService.updateById(bill);

        System.out.println("已更新账单备注，账单ID: " + bill.getId() + "，结算金额: " + settlementAmount);
    }

    /**
     * 更新已缴费停供情况下的账单信息
     *
     * @param bill 账单信息
     * @param refundAmount 应退金额
     */
    private void updateBillForPaidStopSupply(Bill bill, BigDecimal refundAmount) {
        // total_amount 和 paid_amount 保持不变
        String currentRemark = bill.getRemark() != null ? bill.getRemark() : "";
        String newRemark = currentRemark + "\n用户已申请停供，已发起退费申请，金额" + refundAmount + "元";

        bill.setRemark(newRemark);
        bill.setIsHeating(false); // 设置为不供热
        billService.updateById(bill);

        System.out.println("已更新已缴费停供账单，账单ID: " + bill.getId() + "，退费金额: " + refundAmount);
    }

    /**
     * 创建退费申请
     * 在t_refund表中创建一条新的退费申请记录
     *
     * @param bill 账单信息
     * @param refundAmount 退费金额
     */
    private void createRefundApplication(Bill bill, BigDecimal refundAmount) {
        try {
            // 创建退费申请记录
            Refund refund = new Refund();

            // 设置基本信息
            refund.setHouseId(bill.getHouseId()); // 关联房屋ID
            refund.setBillId(bill.getId()); // 关联账单ID
            refund.setRefundAmount(refundAmount); // 退费金额
            refund.setRefundReason("用户中途申请停供，退还多缴费用"); // 退费原因
            refund.setRefundAmount(refundAmount); // 退费金额
            refund.setStatus("pending");
            // 设置时间信息
            Date currentTime = new Date();
            refund.setCreatedAt(currentTime); // 创建时间
            refund.setUpdatedAt(currentTime); // 更新时间

            // 保存退费申请记录
            boolean result = refundService.save(refund);

            if (result) {
                System.out.println("成功创建退费申请：账单ID=" + bill.getId() +
                                  "，退费金额=" + refundAmount + "元" +
                                  "，退费申请ID=" + refund.getId());
            } else {
                System.err.println("创建退费申请失败：账单ID=" + bill.getId() +
                                  "，退费金额=" + refundAmount + "元");
            }

        } catch (Exception e) {
            System.err.println("创建退费申请异常：账单ID=" + bill.getId() +
                              "，退费金额=" + refundAmount + "元" +
                              "，异常信息=" + e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新房屋供热状态为不供热
     *
     * @param houseId 房屋ID
     */
    private void updateHouseHeatingStatus(Long houseId) {
        HouseInfo houseInfo = houseInfoService.getById(houseId);
        if (houseInfo != null) {
            houseInfo.setIsHeating(0); // 设置为不供热
            houseInfoService.updateById(houseInfo);
            System.out.println("已更新房屋供热状态，房屋ID: " + houseId);
        }
    }

    /**
     * 根据房屋ID和供暖年度查找账单
     * @param houseId 房屋ID
     * @param heatingYear 供暖年度
     * @return 账单信息
     */
    private Bill findBillByHouseIdAndYear(Long houseId, Integer heatingYear) {
        QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("house_id", houseId);
        queryWrapper.eq("heat_year", heatingYear);

        List<Bill> bills = billService.list(queryWrapper);
        return bills.isEmpty() ? null : bills.get(0);
    }

}
