package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 欠费记录表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_overdue_records")
public class OverdueRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 欠费记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联账单ID
     */
    private Long billId;

    /**
     * 关联房屋ID
     */
    private Long houseId;

    /**
     * 所属供暖年度
     */
    private Integer heatingYear;

    /**
     * 账单总金额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 已缴金额（元）
     */
    private BigDecimal paidAmount;

    /**
     * 欠费金额（= total_amount - paid_amount）
     */
    private BigDecimal overdueAmount;

    /**
     * 逾期天数（从 due_date 到当前日期）
     */
    private Integer overdueDays;

    /**
     * 滞纳金日利率（取自规则表）
     */
    private BigDecimal penaltyRate;

    /**
     * 滞纳金金额（= overdue_amount * penalty_rate * overdue_days）
     */
    private BigDecimal penaltyAmount;


    /**
     * 状态：active=生效中，cleared=已结清，written_off=已核销
     */
    private String status;

      /**
     * 备注
     */
    private String remark;
    /**
     * 首次逾期日期（即 due_date + 1 天）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstOverdueDate;

    /**
     * 最后更新日期（每日任务刷新）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastUpdatedDate;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 最后修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 非数据库字段 - 房屋信息
     */
    @TableField(exist = false)
    private String roomNo;

    @TableField(exist = false)
    private String heatUnitName;

    @TableField(exist = false)
    private String houseName;

    /**
     * 非数据库字段 - 账单信息
     */
    @TableField(exist = false)
    private String billNo;

    @TableField(exist = false)
    private Date dueDate;

}
