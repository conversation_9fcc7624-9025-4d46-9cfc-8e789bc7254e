-- 欠费记录表
CREATE TABLE `t_overdue_records` ( 
	`id` BIGINT(19) NOT NULL AUTO_INCREMENT COMMENT '欠费记录ID',
	`bill_id` BIGINT(19) NOT NULL COMMENT '关联账单ID',
	`house_id` BIGINT(19) NOT NULL COMMENT '关联房屋ID',
	`heating_year` INT(10) NOT NULL COMMENT '所属供暖年度',
	`total_amount` DECIMAL(10,2) NOT NULL COMMENT '账单总金额（元）',
	`paid_amount` DECIMAL(10,2) NULL DEFAULT '0.00' COMMENT '已缴金额（元）',
	`overdue_amount` DECIMAL(10,2) NOT NULL COMMENT '欠费金额（= total_amount - paid_amount）',
	`overdue_days` INT(10) NOT NULL COMMENT '逾期天数（从 due_date 到当前日期）',
	`penalty_rate` DECIMAL(5,4) NULL DEFAULT '0.0005' COMMENT '滞纳金日利率（取自规则表）',
	`penalty_amount` DECIMAL(10,2) NULL DEFAULT '0.00' COMMENT '滞纳金金额（= overdue_amount * penalty_rate * overdue_days）',
	`status` ENUM('active','cleared','written_off') NOT NULL DEFAULT 'active' COMMENT '状态：active=生效中，cleared=已结清，written_off=已核销' COLLATE 'utf8mb4_0900_ai_ci',
	`first_overdue_date` DATE NOT NULL COMMENT '首次逾期日期（即 due_date + 1 天）',
	`last_updated_date` DATE NOT NULL COMMENT '最后更新日期（每日任务刷新）',
	`created_at` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updated_at` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `uk_bill_unique` (`bill_id`) USING BTREE,
	INDEX `idx_house_id` (`house_id`) USING BTREE,
	INDEX `idx_bill_id` (`bill_id`) USING BTREE,
	INDEX `idx_heating_year` (`heating_year`) USING BTREE,
	INDEX `idx_status` (`status`) USING BTREE,
	CONSTRAINT `t_overdue_records_ibfk_1` FOREIGN KEY (`bill_id`) REFERENCES `t_bill` (`id`) ON UPDATE NO ACTION ON DELETE CASCADE,
	CONSTRAINT `t_overdue_records_ibfk_2` FOREIGN KEY (`house_id`) REFERENCES `t_house` (`id`) ON UPDATE NO ACTION ON DELETE NO ACTION
)
COMMENT='欠费记录表，记录逾期账单的欠费金额、滞纳金及状态'
COLLATE='utf8mb4_0900_ai_ci'
ENGINE=InnoDB;
