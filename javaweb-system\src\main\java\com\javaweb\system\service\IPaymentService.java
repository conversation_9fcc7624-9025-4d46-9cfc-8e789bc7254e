package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Payment;
import com.javaweb.system.query.PaymentQuery;

/**
 * <p>
 * 缴费记录表 服务类
 * </p>
 */
public interface IPaymentService extends IService<Payment> {

    /**
     * 获取缴费记录列表
     * @param paymentQuery 查询条件
     * @return
     */
    JsonResult getList(PaymentQuery paymentQuery);

    /**
     * 添加或编辑缴费记录
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(Payment entity);

    /**
     * 删除缴费记录
     * @param ids ID数组
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

    /**
     * 根据ID获取缴费记录详情
     * @param id 缴费记录ID
     * @return
     */
    JsonResult info(Integer id);
    
    /**
     * 根据住户地址/户号或户主姓名搜索住户信息
     * @param heatUnitId 小区ID
     * @param addressOrRoomNo 住户地址或户号
     * @param ownerName 户主姓名
     * @return 住户信息
     */
    JsonResult searchHouse(Integer heatUnitId, String addressOrRoomNo, String ownerName);
    
    /**
     * 获取指定住户的缴费记录
     * @param heatUnitId 小区ID
     * @param ownerName 住户姓名
     * @param roomNo 房间号
     * @return 缴费记录列表
     */
    JsonResult getPaymentRecords(Integer heatUnitId,String ownerName, String roomNo);


    /**
     * 获取指定账单的缴费记录
     * @param billId 小区ID
     * @return 缴费记录列表
     */
    JsonResult getPaymentRecordsByBillId(Integer billId);

    /**
     * 更新账单和房屋供热状态
     * 缴费成功后更新对应账单的已缴金额和房屋的供热状态
     * @param payment 缴费记录
     * @return 更新结果
     */
    JsonResult updateBillAndHouseStatus(Payment payment);
} 