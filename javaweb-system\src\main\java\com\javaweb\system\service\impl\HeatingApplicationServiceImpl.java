package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HeatingApplication;
import com.javaweb.system.mapper.HeatingApplicationMapper;
import com.javaweb.system.query.HeatingApplicationQuery;
import com.javaweb.system.service.IHeatingApplicationService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用热申请表 服务实现类
 * </p>
 */
@Service
public class HeatingApplicationServiceImpl extends ServiceImpl<HeatingApplicationMapper, HeatingApplication> implements IHeatingApplicationService {

    /**
     * 获取用热申请数据列表
     */
    @Override
    public JsonResult getList(HeatingApplicationQuery heatingApplicationQuery) {
        // 创建分页对象
        IPage<HeatingApplication> page = new Page<>(heatingApplicationQuery.getPage(), heatingApplicationQuery.getLimit());

        // 查询数据
        IPage<HeatingApplication> data = baseMapper.getHeatingApplicationList(page, heatingApplicationQuery);

        return JsonResult.success(data, "查询成功");
    }

    /**
     * 添加或编辑用热申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult edit(HeatingApplication entity) {
        if (entity.getId() != null && entity.getId() > 0) {
            // 编辑
            entity.setUpdatedAt(new Date());
            
            // 检查申请是否已审批，已审批的不能修改
            HeatingApplication existApply = getById(entity.getId());
            if (existApply != null && !"pending".equals(existApply.getCurrentStatus())) {
                return JsonResult.error("该申请已审批，不能修改");
            }
        } else {
            // 新增
            entity.setApplyTime(new Date());
            entity.setCurrentStatus("pending");
            entity.setCreatedAt(new Date());
            entity.setUpdatedAt(new Date());
            entity.setIsNotified(0);
            if (StringUtils.isEmpty(entity.getSource())) {
                entity.setSource("online");
            }
            
            // 检查是否已存在同一房屋同一供暖季的待审批申请
            QueryWrapper<HeatingApplication> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("house_id", entity.getHouseId());
            queryWrapper.eq("apply_season", entity.getApplySeason());
            queryWrapper.eq("current_status", "pending");
            int count = count(queryWrapper);
            if (count > 0) {
                return JsonResult.error("该房屋已存在" + entity.getApplySeason() + "供暖季的待审批用热申请");
            }
        }
        
        boolean result = saveOrUpdate(entity);
        if (result) {
            return JsonResult.success("保存成功");
        } else {
            return JsonResult.error("保存失败");
        }
    }

    /**
     * 删除用热申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteByIds(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        
        // 检查是否有已审批的申请，已审批的不能删除
        QueryWrapper<HeatingApplication> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        queryWrapper.ne("current_status", "pending");
        int count = count(queryWrapper);
        if (count > 0) {
            return JsonResult.error("存在已审批的申请，不能删除");
        }
        
        boolean result = removeByIds(idList);
        if (result) {
            return JsonResult.success("删除成功");
        } else {
            return JsonResult.error("删除失败");
        }
    }

    /**
     * 获取用热申请详情
     */
    @Override
    public JsonResult info(Long id) {
        HeatingApplication entity = getById(id);
        return JsonResult.success(entity, "查询成功");
    }

    /**
     * 审核用热申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult approve(Long id, String status, String reason, String remark) {
        HeatingApplication entity = getById(id);
        if (entity == null) {
            return JsonResult.error("申请不存在");
        }

        if (!"pending".equals(entity.getCurrentStatus())) {
            return JsonResult.error("该申请已审批，不能重复审批");
        }

        if (!"approved".equals(status) && !"rejected".equals(status)) {
            return JsonResult.error("审批状态参数错误");
        }

        // 更新申请状态
        entity.setCurrentStatus(status);
        entity.setApprovedBy(ShiroUtils.getUserName());
        entity.setApproveTime(new Date());
        entity.setUpdatedAt(new Date());
        
        if (StringUtils.isNotEmpty(reason)) {
            if ("rejected".equals(status)) {
                entity.setRejectReason(reason);
            }
        }
        
        if (StringUtils.isNotEmpty(remark)) {
            entity.setRemark(remark);
        }

        boolean result = updateById(entity);
        if (!result) {
            return JsonResult.error("审批失败");
        }

        // 如果审批同意，可以在这里添加后续业务逻辑
        if ("approved".equals(status)) {
            try {
                // 处理审批通过后的业务逻辑，比如生成账单、更新房屋状态等
                handleApprovedApplication(entity);
            } catch (Exception e) {
                // 如果处理失败，抛出异常回滚事务
                throw new RuntimeException("处理审批通过后的业务逻辑失败：" + e.getMessage(), e);
            }
        }

        return JsonResult.success("审批成功");
    }

    /**
     * 处理审批通过后的业务逻辑
     * @param application 用热申请信息
     */
    private void handleApprovedApplication(HeatingApplication application) {
        // 这里可以添加审批通过后的业务逻辑
        // 比如：
        // 1. 生成供暖账单
        // 2. 更新房屋供热状态
        // 3. 发送通知等
        // 暂时只是标记为已通知
        application.setIsNotified(1);
        updateById(application);
    }

}
