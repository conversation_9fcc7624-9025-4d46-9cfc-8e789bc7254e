<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.StopSupplyApplyMapper">

    <!-- 获取申请停供列表（带房屋信息） -->
    <select id="getStopSupplyApplyList" resultType="com.javaweb.system.entity.StopSupplyApply">
        SELECT 
            ssa.*,
            h.room_no,
            h.house_master as houseName,
            hu.name as heatUnitName,
            u.realname as approverName
        FROM t_stop_supply_apply ssa
        LEFT JOIN t_house h ON ssa.house_id = h.id
        LEFT JOIN t_heat_unit hu ON h.heat_unit_id = hu.id
        LEFT JOIN t_user u ON ssa.approved_by = u.id
        <where>
            <if test="query.heatUnitId != null and query.heatUnitId > 0">
                AND h.heat_unit_id = #{query.heatUnitId}
            </if>
            <if test="query.roomNo != null and query.roomNo != ''">
                AND h.room_no LIKE CONCAT('%', #{query.roomNo}, '%')
            </if>
            <if test="query.heatingYear != null">
                AND ssa.heating_year = #{query.heatingYear}
            </if>
            <if test="query.status != null and query.status.size() > 0">
                AND ssa.status IN
                <foreach collection="query.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.applyStartDate != null and query.applyStartDate != ''">
                AND ssa.apply_date >= #{query.applyStartDate}
            </if>
            <if test="query.applyEndDate != null and query.applyEndDate != ''">
                AND ssa.apply_date &lt;= #{query.applyEndDate}
            </if>
            <if test="query.stopStartDate != null and query.stopStartDate != ''">
                AND ssa.stop_start_date >= #{query.stopStartDate}
            </if>
            <if test="query.stopEndDate != null and query.stopEndDate != ''">
                AND ssa.stop_end_date &lt;= #{query.stopEndDate}
            </if>
            <if test="query.approvedBy != null">
                AND ssa.approved_by = #{query.approvedBy}
            </if>
        </where>
        ORDER BY ssa.created_at DESC
    </select>

</mapper>
