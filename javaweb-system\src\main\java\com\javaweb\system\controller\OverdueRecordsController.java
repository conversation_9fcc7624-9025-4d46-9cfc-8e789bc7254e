package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.OverdueRecords;
import com.javaweb.system.query.OverdueRecordsQuery;
import com.javaweb.system.service.IOverdueRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 欠费记录 前端控制器
 * </p>
 */
@RestController
@RequestMapping("/overdueRecords")
public class OverdueRecordsController extends BaseController {

    @Autowired
    private IOverdueRecordsService overdueRecordsService;

    /**
     * 获取欠费记录列表
     * @param overdueRecordsQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(OverdueRecordsQuery overdueRecordsQuery) {
        return overdueRecordsService.getList(overdueRecordsQuery);
    }

    /**
     * 删除欠费记录
     * @param ids ID数组
     * @return
     */
    @Log(title = "欠费记录", logType = LogType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public JsonResult delete(@PathVariable("ids") Long[] ids) {
        return overdueRecordsService.deleteByIds(ids);
    }

    /**
     * 获取欠费记录详情
     * @param id 记录ID
     * @return
     */
    @GetMapping("/info/{id}")
    public JsonResult info(@PathVariable("id") Long id) {
        return overdueRecordsService.info(id);
    }

    /**
     * 核销欠费记录
     * @param id 记录ID
     * @param reason 核销原因
     * @return
     */
    @Log(title = "核销欠费记录", logType = LogType.UPDATE)
    @PostMapping("/writeOff")
    public JsonResult writeOff(@RequestParam Long id, 
                              @RequestParam(required = false) String reason) {
        return overdueRecordsService.writeOff(id, reason);
    }

    /**
     * 批量核销欠费记录
     * @param ids ID数组
     * @param reason 核销原因
     * @return
     */
    @Log(title = "批量核销欠费记录", logType = LogType.UPDATE)
    @PostMapping("/batchWriteOff")
    public JsonResult batchWriteOff(@RequestParam Long[] ids, 
                                   @RequestParam(required = false) String reason) {
        return overdueRecordsService.batchWriteOff(ids, reason);
    }

}
