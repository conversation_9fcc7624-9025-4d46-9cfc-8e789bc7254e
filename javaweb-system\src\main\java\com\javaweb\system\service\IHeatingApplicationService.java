package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatingApplication;
import com.javaweb.system.query.HeatingApplicationQuery;

/**
 * <p>
 * 用热申请表 服务类
 * </p>
 */
public interface IHeatingApplicationService extends IService<HeatingApplication> {

    /**
     * 获取用热申请数据列表
     * @param heatingApplicationQuery 查询条件
     * @return
     */
    JsonResult getList(HeatingApplicationQuery heatingApplicationQuery);

    /**
     * 添加或编辑用热申请
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(HeatingApplication entity);

    /**
     * 删除用热申请
     * @param ids ID数组
     * @return
     */
    JsonResult deleteByIds(Long[] ids);

    /**
     * 获取用热申请详情
     * @param id 申请ID
     * @return
     */
    JsonResult info(Long id);

    /**
     * 审核用热申请
     * @param id 申请ID
     * @param status 审核状态（approved=批准，rejected=拒绝）
     * @param reason 审核意见
     * @param remark 管理员备注
     * @return
     */
    JsonResult approve(Long id, String status, String reason, String remark);

}
