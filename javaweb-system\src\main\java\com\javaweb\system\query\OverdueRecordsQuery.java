package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 欠费记录查询条件
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OverdueRecordsQuery extends BaseQuery {

    /**
     * 小区ID
     */
    private Long heatUnitId;

    /**
     * 房号
     */
    private String roomNo;

    /**
     * 供暖年度
     */
    private Integer heatingYear;

    /**
     * 状态
     */
    private List<String> status;

    /**
     * 最小欠费金额
     */
    private BigDecimal minOverdueAmount;

    /**
     * 最大欠费金额
     */
    private BigDecimal maxOverdueAmount;

    /**
     * 最小逾期天数
     */
    private Integer minOverdueDays;

    /**
     * 最大逾期天数
     */
    private Integer maxOverdueDays;

    /**
     * 首次逾期开始日期
     */
    private String firstOverdueStartDate;

    /**
     * 首次逾期结束日期
     */
    private String firstOverdueEndDate;

}
