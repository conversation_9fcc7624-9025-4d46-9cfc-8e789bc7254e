# 停供申请审批业务逻辑实现说明

## 概述

根据《审批停供.txt》文档的业务需求，重新实现了StopSupplyApplyServiceImpl.java中的管理员审批通过后的业务逻辑，实现了智能化的停供处理流程。

## 业务场景分类

### 场景1：供暖开始前申请并获批停供
**触发条件**：停供生效日期 < 供暖开始日期
**处理逻辑**：直接修改审批状态即可，无需复杂的财务计算

### 场景2：供暖开始后申请停供，且此前未缴费
**触发条件**：停供生效日期 >= 供暖开始日期 且 已缴金额 = 0
**处理逻辑**：计算结算金额，核销欠费记录，免除滞纳金

### 场景3：供暖开始后申请停供，但此前已全额缴费
**触发条件**：停供生效日期 >= 供暖开始日期 且 已缴金额 >= 应缴总金额
**处理逻辑**：计算应退金额，创建退费申请

## 核心算法实现

### 结算金额计算算法
```java
private BigDecimal calculateSettlementAmount(BigDecimal totalAmount, Date effectiveDate, 
                                           Date heatingStartDate, Date heatingEndDate, HeatingFeeRule activeRule)
```

**计算步骤**：
1. **计算A**：按实际供暖天数计算费用
   - 实际天数 = 停供生效日 - 供暖开始日
   - 实际供暖费用 = (总金额 / 总天数) × 实际天数

2. **计算B**：按最低缴费比例计算基础热损费
   - 基础热损费 = 总金额 × min_payment_rate

3. **最终结算金额** = MAX(计算A, 计算B)

### 计算示例
```
假设：
- 总金额：2500元
- 供暖期：2024-11-15 至 2025-03-15（120天）
- 停供生效日：2024-12-15（供暖30天后）
- 最低缴费比例：30%

计算A：实际供暖费用 = 2500 × (30/120) = 625元
计算B：基础热损费 = 2500 × 0.3 = 750元
最终结算金额 = MAX(625, 750) = 750元
```

## 主要方法说明

### 1. updateBillAndHouseStatus()
**功能**：管理员审批通过后的主要业务逻辑入口
**流程**：
- 获取供暖计费规则
- 查找对应账单
- 根据停供生效日期判断处理场景
- 调用相应的处理方法
- 更新房屋供热状态

### 2. handlePreHeatingStopSupply()
**功能**：处理供暖开始前的停供申请
**逻辑**：直接修改审批状态，无需复杂处理

### 3. handlePostHeatingStopSupply()
**功能**：处理供暖开始后的停供申请
**逻辑**：
- 计算结算金额
- 根据缴费状态分别处理未缴费和已缴费情况

### 4. handlePostHeatingUnpaidStopSupply()
**功能**：处理未缴费的停供申请（场景2）
**逻辑**：
- 核销欠费记录
- 更新账单备注
- 免除滞纳金

### 5. handlePostHeatingPaidStopSupply()
**功能**：处理已缴费的停供申请（场景3）
**逻辑**：
- 计算应退金额
- 创建退费申请
- 更新账单备注

## 数据库操作详解

### 欠费记录表（t_overdue_records）处理
**场景2中的操作**：
```java
// 查询与该账单关联的欠费记录
QueryWrapper<OverdueRecords> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("bill_id", bill.getId());
OverdueRecords overdueRecord = overdueRecordsService.getOne(queryWrapper);

if (overdueRecord != null) {
    // 将状态从 active 更新为 written_off（已核销）
    overdueRecord.setStatus("written_off");
    // 添加核销原因说明
    overdueRecord.setUpdatedAt(new Date());
    overdueRecordsService.updateById(overdueRecord);
}
```

### 账单表（t_bill）处理

**场景2（未缴费）的账单更新**：
```java
// 绝不修改 total_amount，保持 status 为 overdue
String newRemark = currentRemark + "\n已生成停供结算，实际应收金额：" + settlementAmount + "元，滞纳金已免除";
bill.setRemark(newRemark);
bill.setIsHeating(false); // 设置为不供热
billService.updateById(bill);
```

**场景3（已缴费）的账单更新**：
```java
// total_amount 和 paid_amount 保持不变
String newRemark = currentRemark + "\n用户已申请停供，已发起退费申请，金额" + refundAmount + "元";
bill.setRemark(newRemark);
bill.setIsHeating(false); // 设置为不供热
billService.updateById(bill);
```

### 房屋信息表（t_house）处理
```java
private void updateHouseHeatingStatus(Long houseId) {
    HouseInfo houseInfo = houseInfoService.getById(houseId);
    if (houseInfo != null) {
        houseInfo.setIsHeating(0); // 设置为不供热
        houseInfoService.updateById(houseInfo);
    }
}
```

## 业务规则遵循

### 1. 滞纳金免除规则
- **原则**：因为用户申请停供，视为主动终止服务
- **实现**：在场景2中，将欠费记录状态设为"written_off"，不再计算滞纳金

### 2. 账单金额保护规则
- **原则**：绝不修改账单的total_amount字段
- **实现**：所有场景都保持原始账单金额不变，通过备注说明实际应收金额

### 3. 退费处理规则
- **原则**：已缴费用户申请停供时，退还多缴部分
- **实现**：计算应退金额 = 已缴金额 - 最终应收金额

## 日志和调试信息

### 计算过程日志
```java
System.out.println("结算金额计算：总金额=" + totalAmount + "，实际天数=" + actualDays + 
                  "，总天数=" + totalDays + "，实际供暖费=" + actualHeatingFee + 
                  "，基础热损费=" + baseHeatLossFee + "，最终结算金额=" + settlementAmount);
```

### 操作结果日志
```java
System.out.println("已核销欠费记录，账单ID: " + bill.getId() + "，结算金额: " + settlementAmount);
System.out.println("已更新账单备注，账单ID: " + bill.getId() + "，结算金额: " + settlementAmount);
```

## 扩展功能预留

### 退费申请功能
```java
// TODO: 实际实现时需要：
// 1. 在 t_refund 表中创建一条新记录
// 2. payment_id：关联用户已支付的那笔 t_payment 记录
// 3. refund_amount：退费金额
// 4. refund_reason："用户中途申请停供，退还多缴费用"
// 5. status：pending
```

## 注意事项

### 1. 实体字段兼容性
- 部分实体类可能缺少某些字段（如remark、updatedAt）
- 代码中已添加注释说明，可根据实际情况调整

### 2. 事务管理
- 所有操作都在同一事务中执行
- 任何步骤失败都会触发回滚，保证数据一致性

### 3. 异常处理
- 添加了完善的异常处理机制
- 关键步骤都有异常捕获和错误信息输出

### 4. 性能考虑
- 使用MyBatis-Plus的QueryWrapper进行高效查询
- 避免不必要的数据库操作

这个实现完全遵循了审批停供文档中的业务逻辑，确保了停供申请审批过程的准确性和完整性。
