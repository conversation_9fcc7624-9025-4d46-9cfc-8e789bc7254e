package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 申请停供查询条件
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StopSupplyApplyQuery extends BaseQuery {

    /**
     * 小区ID
     */
    private Long heatUnitId;

    /**
     * 房号
     */
    private String roomNo;

    /**
     * 供暖年度
     */
    private Integer heatingYear;

    /**
     * 审批状态
     */
    private List<String> status;

    /**
     * 申请开始日期
     */
    private String applyStartDate;

    /**
     * 申请结束日期
     */
    private String applyEndDate;

    /**
     * 停供开始日期
     */
    private String stopStartDate;

    /**
     * 停供结束日期
     */
    private String stopEndDate;

    /**
     * 审批人ID
     */
    private Long approvedBy;

}
