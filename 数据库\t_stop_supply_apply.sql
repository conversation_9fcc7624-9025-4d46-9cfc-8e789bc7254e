-- 申请停供表
CREATE TABLE t_stop_supply_apply (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '停供申请ID',
    house_id BIGINT NOT NULL COMMENT '房屋ID',
    apply_date DATE NOT NULL COMMENT '申请日期',
    stop_start_date DATE NOT NULL COMMENT '停供开始日期（用户填写）',
    stop_end_date DATE COMMENT '停供结束日期（可为空，表示长期停供）',
    heating_year INT NOT NULL COMMENT '所属供暖年度',
    status ENUM('pending', 'approved', 'rejected', 'canceled') DEFAULT 'pending' COMMENT '审批状态',
    reason TEXT COMMENT '申请原因',
    approved_by BIGINT COMMENT '审批人ID',
    approved_at DATETIME COMMENT '审批时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (house_id) REFERENCES t_house(id)
) COMMENT='用户中途/提前申请停供记录';

-- 创建索引
CREATE INDEX idx_house_id ON t_stop_supply_apply(house_id);
CREATE INDEX idx_heating_year ON t_stop_supply_apply(heating_year);
CREATE INDEX idx_status ON t_stop_supply_apply(status);
CREATE INDEX idx_apply_date ON t_stop_supply_apply(apply_date);
CREATE INDEX idx_approved_by ON t_stop_supply_apply(approved_by);
