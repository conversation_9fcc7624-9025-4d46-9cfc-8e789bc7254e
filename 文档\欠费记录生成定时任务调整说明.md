# 欠费记录生成定时任务调整说明

## 修改概述

根据需求调整了WorkThreadServer.java中的`generateOverdueRecordsTask`定时任务，重新实现了欠费记录生成逻辑，按照供暖开始日期和逾期规则处理未缴费账单。

## 主要修改内容

### 1. 依赖注入调整

**新增依赖**：
```java
@Autowired
private IHouseInfoService houseInfoService; // 房屋信息服务

import com.javaweb.system.service.IHouseInfoService; // 房屋信息服务接口
```

### 2. 核心业务逻辑重构

**修改前的逻辑**：
- 查找所有逾期且未完全缴费的账单
- 基于缴费截止日期计算逾期天数
- 按原账单金额计算欠费和滞纳金

**修改后的逻辑**：
- 判断当前日期是否已达到供暖开始日期
- 查找所有状态为unpaid（未缴费）的账单
- 判断是否超过逾期天数：
  - **超过逾期天数**：按最低缴费比例计算欠费金额，停止住户供热
  - **未超过逾期天数**：按全额计算欠费金额，保持住户供热状态

## 详细业务流程

### 1. 供暖开始日期判断

```java
// 判断当前日期是否已达到供暖开始日期
Date heatingStartDate = activeRule.getHeatingStartDate();
if (currentDate.before(heatingStartDate)) {
    log.info("当前日期[{}]未达到供暖开始日期[{}]，跳过欠费记录生成", 
        today, heatingStartDate);
    return;
}
```

**业务规则**：
- 只有当前日期 >= 供暖开始日期时，才执行欠费记录生成
- 供暖开始前不处理任何欠费记录

### 2. 未缴费账单查询

```java
/**
 * 查询所有状态为unpaid（未缴费）的账单
 */
private List<Bill> getUnpaidBills() {
    QueryWrapper<Bill> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("status", "unpaid"); // 查询状态为unpaid的账单
    return billMapper.selectList(queryWrapper);
}
```

**查询条件**：
- 账单状态 = "unpaid"
- 不再基于缴费截止日期判断

### 3. 欠费记录创建逻辑

#### 3.1 逾期判断和欠费金额计算

```java
// 判断是否超过逾期天数
boolean isOverdue = overdueDays > activeRule.getDueDay();

// 根据是否超过逾期天数计算欠费金额
BigDecimal overdueAmount;
String remark;

if (isOverdue) {
    // 超过逾期天数：按最低缴费比例计算欠费金额
    overdueAmount = bill.getTotalAmount()
        .multiply(activeRule.getMinPaymentRate())
        .setScale(2, java.math.RoundingMode.HALF_UP);
    remark = "超过逾期天数，按最低缴费比例计算欠费金额";
} else {
    // 未超过逾期天数：按全额计算欠费金额
    overdueAmount = bill.getTotalAmount();
    remark = "未超过逾期天数，按全额计算欠费金额";
}
```

**计算规则**：
- **超过逾期天数**：欠费金额 = 账单总金额 × 最低缴费比例
- **未超过逾期天数**：欠费金额 = 账单总金额（全额）

#### 3.2 滞纳金计算

```java
// 计算滞纳金：欠费金额 × 滞纳金日利率 × 逾期天数
BigDecimal penaltyAmount = BigDecimal.ZERO;
if (overdueDays > 0) {
    penaltyAmount = overdueAmount
        .multiply(activeRule.getPenaltyRate())
        .multiply(BigDecimal.valueOf(overdueDays))
        .setScale(2, java.math.RoundingMode.HALF_UP);
}
```

**计算规则**：
- 滞纳金 = 欠费金额 × 滞纳金日利率 × 逾期天数
- 逾期天数 = 当前日期 - 缴费截止日期
- 逾期天数 <= 0 时，滞纳金为0

#### 3.3 欠费记录字段设置

```java
OverdueRecords overdueRecord = new OverdueRecords();
overdueRecord.setBillId(bill.getId().longValue());
overdueRecord.setHouseId(bill.getHouseId().longValue());
overdueRecord.setHeatingYear(bill.getHeatYear());
overdueRecord.setTotalAmount(bill.getTotalAmount());
overdueRecord.setPaidAmount(BigDecimal.ZERO); // 未缴费账单已缴金额为0
overdueRecord.setOverdueAmount(overdueAmount); // 欠费金额（根据逾期情况计算）
overdueRecord.setOverdueDays((int) Math.max(0, overdueDays));
overdueRecord.setPenaltyRate(activeRule.getPenaltyRate());
overdueRecord.setPenaltyAmount(penaltyAmount);
overdueRecord.setStatus("active"); // 生效中
overdueRecord.setRemark(remark); // 动态生成的备注说明
```

### 4. 停止供热逻辑

#### 4.1 逾期判断

```java
// 判断是否超过逾期天数
boolean isOverdue = overdueDays > activeRule.getDueDay();
```

**判断规则**：
- 逾期天数 > 计费规则中的逾期天数（due_day）
- 满足条件时需要停止住户供热

#### 4.2 停止供热操作

```java
/**
 * 停止指定住户的供热服务
 * 将住户表的is_heating字段设置为0（不供热）
 */
private void stopHeatingForHouse(Integer houseId) {
    // 获取房屋信息
    HouseInfo houseInfo = houseInfoService.getById(houseId);
    
    // 检查当前供热状态
    if (houseInfo.getIsHeating() != null && houseInfo.getIsHeating() == 0) {
        log.debug("房屋[{}]已经是不供热状态，无需重复设置", houseId);
        return;
    }
    
    // 设置为不供热状态
    houseInfo.setIsHeating(0);
    houseInfoService.updateById(houseInfo);
}
```

**操作步骤**：
1. 获取房屋信息
2. 检查当前供热状态，避免重复设置
3. 将is_heating字段设置为0（不供热）
4. 保存更新到数据库

### 5. 已存在欠费记录的处理

```java
/**
 * 检查并更新已存在的欠费记录状态
 */
private boolean checkAndUpdateOverdueStatus(OverdueRecords existingRecord, Bill bill,
                                          HeatingFeeRule activeRule, LocalDate today) {
    // 计算当前逾期天数
    long currentOverdueDays = ChronoUnit.DAYS.between(dueDate, today);
    
    // 更新逾期天数
    existingRecord.setOverdueDays((int) Math.max(0, currentOverdueDays));
    
    // 重新计算滞纳金
    BigDecimal baseHeatingFee = existingRecord.getOverdueAmount();
    BigDecimal newPenaltyAmount = baseHeatingFee
        .multiply(activeRule.getPenaltyRate())
        .multiply(BigDecimal.valueOf(currentOverdueDays));
    
    existingRecord.setPenaltyAmount(newPenaltyAmount);
    overdueRecordsService.updateById(existingRecord);
    
    // 判断是否需要停止供热
    return currentOverdueDays > activeRule.getDueDay();
}
```

## 计算示例

### 示例1：超过逾期天数的情况

**假设条件**：
- 账单总金额：2500元
- 最低缴费比例：30%
- 缴费截止日期：2024-10-15
- 当前日期：2024-11-20
- 逾期天数限制：30天
- 滞纳金日利率：0.05%

**计算过程**：
1. 逾期天数 = 2024-11-20 - 2024-10-15 = 36天
2. 是否超期 = 36 > 30 = true（超过逾期天数）
3. 欠费金额 = 2500 × 0.3 = 750元（按最低缴费比例）
4. 滞纳金 = 750 × 0.0005 × 36 = 13.5元
5. 需要停止供热

**欠费记录**：
```json
{
  "bill_id": 123,
  "house_id": 456,
  "heating_year": 2024,
  "total_amount": 2500.00,
  "paid_amount": 0.00,
  "overdue_amount": 750.00,
  "overdue_days": 36,
  "penalty_amount": 13.50,
  "status": "active",
  "remark": "超过逾期天数，按最低缴费比例计算欠费金额"
}
```

### 示例2：未超过逾期天数的情况

**假设条件**：
- 账单总金额：2500元
- 缴费截止日期：2024-10-15
- 当前日期：2024-11-10
- 逾期天数限制：30天
- 滞纳金日利率：0.05%

**计算过程**：
1. 逾期天数 = 2024-11-10 - 2024-10-15 = 26天
2. 是否超期 = 26 > 30 = false（未超过逾期天数）
3. 欠费金额 = 2500元（按全额计算）
4. 滞纳金 = 2500 × 0.0005 × 26 = 32.5元
5. 不停止供热，保持供热状态

**欠费记录**：
```json
{
  "bill_id": 124,
  "house_id": 457,
  "heating_year": 2024,
  "total_amount": 2500.00,
  "paid_amount": 0.00,
  "overdue_amount": 2500.00,
  "overdue_days": 26,
  "penalty_amount": 32.50,
  "status": "active",
  "remark": "未超过逾期天数，按全额计算欠费金额"
}
```

## 日志记录

### 1. 任务执行日志
```java
log.info("开始执行欠费记录生成定时任务...");
log.info("当前日期[{}]已达到供暖开始日期[{}]，开始处理未缴费账单", today, heatingStartDate);
log.info("找到 {} 条未缴费账单", unpaidBills.size());
log.info("欠费记录生成任务执行完成，处理账单: {} 条，停止供热住户: {} 户", processedCount, stopHeatingCount);
```

### 2. 业务操作日志
```java
log.info("为账单[{}]创建欠费记录，逾期{}天，基础用热费{}元，滞纳金{}元，是否超期:{}",
    bill.getId(), overdueDays, baseHeatingFee, penaltyAmount, isOverdue);

log.info("成功停止房屋[{}]的供热服务，房号：{}", houseId, houseInfo.getRoomNo());
```

### 3. 异常处理日志
```java
log.error("处理账单[{}]时发生异常: {}", bill.getId(), e.getMessage(), e);
log.error("停止房屋[{}]供热服务时发生异常: {}", houseId, e.getMessage(), e);
```

## 数据库操作

### 1. 欠费记录表（t_overdue_records）
- **新增记录**：为每个未缴费账单创建欠费记录
- **更新记录**：更新已存在记录的逾期天数和滞纳金

### 2. 房屋信息表（t_house）
- **更新字段**：将is_heating字段设置为0（不供热）
- **更新条件**：逾期天数超过规定限制

## 技术特点

### 1. 完善的中文注释
- 每个方法都有详细的中文注释
- 业务逻辑说明清晰
- 参数和返回值说明完整

### 2. 异常处理机制
- 完善的异常捕获和日志记录
- 单个账单处理失败不影响整体任务
- 详细的错误信息便于排查

### 3. 业务规则严格执行
- 严格按照供暖开始日期判断
- 按最低缴费比例计算欠费金额
- 准确的逾期天数计算和停热判断

### 4. 性能优化
- 批量查询未缴费账单
- 避免重复设置供热状态
- 高效的数据库操作

## 注意事项

### 1. 数据库字段兼容性
- 确保HouseInfo表有is_heating字段
- 确保OverdueRecords表有remark字段

### 2. 计费规则依赖
- 必须有生效的供暖计费规则
- 规则中必须包含供暖开始日期、最低缴费比例、逾期天数等

### 3. 定时任务执行
- 每天凌晨00:30执行
- 建议在系统负载较低时执行

### 4. 业务影响
- 停止供热操作不可逆，需要谨慎处理
- 建议在测试环境充分验证后再上线

这次调整完全按照您的要求重新实现了欠费记录生成逻辑，确保了业务规则的准确执行和系统的稳定运行。
