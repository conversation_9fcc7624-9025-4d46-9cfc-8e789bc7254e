package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.OverdueRecords;
import com.javaweb.system.mapper.OverdueRecordsMapper;
import com.javaweb.system.query.OverdueRecordsQuery;
import com.javaweb.system.service.IOverdueRecordsService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 欠费记录表 服务实现类
 * </p>
 */
@Service
public class OverdueRecordsServiceImpl extends ServiceImpl<OverdueRecordsMapper, OverdueRecords> implements IOverdueRecordsService {

    @Autowired
    private OverdueRecordsMapper overdueRecordsMapper;

    /**
     * 获取欠费记录数据列表
     */
    @Override
    public JsonResult getList(OverdueRecordsQuery overdueRecordsQuery) {
        // 分页查询
        Page<OverdueRecords> page = new Page<>(overdueRecordsQuery.getPage(), overdueRecordsQuery.getLimit());
        IPage<OverdueRecords> data = overdueRecordsMapper.getOverdueRecordsList(page, overdueRecordsQuery);
        
        return JsonResult.success(data, "查询成功");
    }

    /**
     * 删除欠费记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteByIds(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);
        
        // 检查是否有已结清或已核销的记录，这些不能删除
        QueryWrapper<OverdueRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        queryWrapper.in("status", Arrays.asList("cleared", "written_off"));
        int count = count(queryWrapper);
        if (count > 0) {
            return JsonResult.error("存在已结清或已核销的记录，不能删除");
        }
        
        boolean result = removeByIds(idList);
        if (result) {
            return JsonResult.success("删除成功");
        } else {
            return JsonResult.error("删除失败");
        }
    }

    /**
     * 获取欠费记录详情
     */
    @Override
    public JsonResult info(Long id) {
        OverdueRecords entity = getById(id);
        return JsonResult.success(entity, "查询成功");
    }

    /**
     * 核销欠费记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult writeOff(Long id, String reason) {
        OverdueRecords entity = getById(id);
        if (entity == null) {
            return JsonResult.error("欠费记录不存在");
        }
        
        if (!"active".equals(entity.getStatus())) {
            return JsonResult.error("只有生效中的记录才能核销");
        }
        
        entity.setStatus("written_off");
        entity.setUpdatedAt(new Date());
        entity.setLastUpdatedDate(new Date());
        
        boolean result = updateById(entity);
        if (result) {
            return JsonResult.success("核销成功");
        } else {
            return JsonResult.error("核销失败");
        }
    }

    /**
     * 批量核销欠费记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult batchWriteOff(Long[] ids, String reason) {
        List<Long> idList = Arrays.asList(ids);
        
        // 检查是否有非生效中的记录
        QueryWrapper<OverdueRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        queryWrapper.ne("status", "active");
        int count = count(queryWrapper);
        if (count > 0) {
            return JsonResult.error("存在非生效中的记录，不能核销");
        }
        
        // 批量更新状态
        QueryWrapper<OverdueRecords> updateWrapper = new QueryWrapper<>();
        updateWrapper.in("id", idList);
        
        OverdueRecords updateEntity = new OverdueRecords();
        updateEntity.setStatus("written_off");
        updateEntity.setUpdatedAt(new Date());
        updateEntity.setLastUpdatedDate(new Date());
        
        boolean result = update(updateEntity, updateWrapper);
        if (result) {
            return JsonResult.success("批量核销成功");
        } else {
            return JsonResult.error("批量核销失败");
        }
    }

}
