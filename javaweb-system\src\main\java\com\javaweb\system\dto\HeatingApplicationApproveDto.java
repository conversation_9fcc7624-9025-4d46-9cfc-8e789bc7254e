package com.javaweb.system.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * <p>
 * 用热申请审批参数
 * </p>
 */
@Data
public class HeatingApplicationApproveDto {

    /**
     * 申请ID
     */
    private Long id;

    /**
     * 审批状态（approved=批准，rejected=拒绝）
     */
    private String status;

    /**
     * 审批意见
     */
    private String reason;

    /**
     * 管理员备注
     */
    private String remark;

    /**
     * 支付金额（审批同意时必填）
     */
    private BigDecimal paidAmount;

}
