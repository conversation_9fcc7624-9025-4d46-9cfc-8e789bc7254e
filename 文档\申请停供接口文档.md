# 申请停供接口文档

## 概述
申请停供模块提供了完整的停供申请管理功能，包括申请提交、审核、查询等操作。

## 接口列表

### 1. 获取申请停供列表
**接口地址：** `GET /stopSupplyApply/index`

**请求参数：**
```json
{
  "page": 1,                    // 页码
  "limit": 10,                  // 每页数量
  "heatUnitId": 1,             // 小区ID（可选）
  "heatUnitFloorId": 1,        // 楼宇ID（可选）
  "heatUnitFloorUnitId": 1,    // 单元ID（可选）
  "roomNo": "101",             // 房号（可选）
  "heatingYear": 2024,         // 供暖年度（可选）
  "status": ["pending"],       // 审批状态数组（可选）
  "applyStartDate": "2024-01-01", // 申请开始日期（可选）
  "applyEndDate": "2024-12-31",   // 申请结束日期（可选）
  "approvedBy": 1              // 审批人ID（可选）
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "houseId": 123,
        "applyDate": "2024-01-15",
        "stopStartDate": "2024-02-01",
        "stopEndDate": "2024-04-30",
        "heatingYear": 2024,
        "status": "pending",
        "reason": "长期出差",
        "approvedBy": null,
        "approvedAt": null,
        "createdAt": "2024-01-15 10:30:00",
        "updatedAt": "2024-01-15 10:30:00",
        "roomNo": "101",
        "heatUnitName": "阳光小区",
        "approverName": null
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 添加申请停供
**接口地址：** `POST /stopSupplyApply/add`

**请求参数：**
```json
{
  "houseId": 123,
  "stopStartDate": "2024-02-01",
  "stopEndDate": "2024-04-30",
  "heatingYear": 2024,
  "reason": "长期出差，房屋无人居住"
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "保存成功"
}
```

### 3. 编辑申请停供
**接口地址：** `PUT /stopSupplyApply/edit`

**请求参数：**
```json
{
  "id": 1,
  "houseId": 123,
  "stopStartDate": "2024-02-01",
  "stopEndDate": "2024-04-30",
  "heatingYear": 2024,
  "reason": "长期出差，房屋无人居住（修改）"
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "保存成功"
}
```

### 4. 删除申请停供
**接口地址：** `DELETE /stopSupplyApply/delete/{ids}`

**请求参数：**
- ids: 申请ID数组，如 `1,2,3`

**返回结果：**
```json
{
  "code": 0,
  "msg": "删除成功"
}
```

### 5. 获取申请停供详情
**接口地址：** `GET /stopSupplyApply/info/{id}`

**请求参数：**
- id: 申请ID

**返回结果：**
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "houseId": 123,
    "applyDate": "2024-01-15",
    "stopStartDate": "2024-02-01",
    "stopEndDate": "2024-04-30",
    "heatingYear": 2024,
    "status": "pending",
    "reason": "长期出差",
    "approvedBy": null,
    "approvedAt": null,
    "createdAt": "2024-01-15 10:30:00",
    "updatedAt": "2024-01-15 10:30:00"
  }
}
```

### 6. 审核申请停供
**接口地址：** `POST /stopSupplyApply/approve`

**请求参数：**
```json
{
  "id": 1,
  "status": "approved",  // approved=批准, rejected=拒绝
  "reason": "审核通过"   // 审核意见（可选）
}
```

**返回结果：**
```json
{
  "code": 0,
  "msg": "审批成功"
}
```

## 状态说明
- `pending`: 待审批
- `approved`: 已批准
- `rejected`: 已拒绝
- `canceled`: 已取消

## 业务规则
1. 每个房屋每个供暖年度只能有一个待审批的申请
2. 已审批的申请不能修改或删除
3. 只有待审批状态的申请才能进行审核操作
4. 审核时会自动记录审核人和审核时间
