package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.system.entity.StopSupplyApply;
import com.javaweb.system.query.StopSupplyApplyQuery;
import org.apache.ibatis.annotations.Param;



/**
 * <p>
 * 申请停供表 Mapper 接口
 * </p>
 */
public interface StopSupplyApplyMapper extends BaseMapper<StopSupplyApply> {

    /**
     * 获取申请停供列表（带房屋信息）
     * @param page 分页对象
     * @param query 查询条件
     * @return
     */
    IPage<StopSupplyApply> getStopSupplyApplyList(Page<StopSupplyApply> page, @Param("query") StopSupplyApplyQuery query);

}
