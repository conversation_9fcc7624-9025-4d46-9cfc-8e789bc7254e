# 申请停供审批接口修复说明

## 问题描述

用户在申请停供审批界面点击"提交审批"时，出现"Request failed with status code 400"错误。

## 问题分析

通过分析前端和后端代码，发现了参数传递格式不匹配的问题：

### 前端发送格式
```javascript
this.$http.post('/stopSupplyApply/approve', {
  id: this.data.id,
  status: this.form.status,
  reason: this.form.reason
})
```
前端发送的是**JSON格式的请求体**。

### 后端接收格式（修复前）
```java
@PostMapping("/approve")
public JsonResult approve(@RequestParam Long id, 
                         @RequestParam String status, 
                         @RequestParam(required = false) String reason)
```
后端使用`@RequestParam`注解，期望接收**表单参数或URL参数**，而不是JSON请求体。

### 问题根因
- 前端发送JSON请求体
- 后端期望表单参数
- 参数格式不匹配导致400错误

## 解决方案

### 1. 创建DTO类
创建`StopSupplyApproveDto`类来封装审批参数：

```java
@Data
public class StopSupplyApproveDto {
    private Long id;        // 申请ID
    private String status;  // 审批状态（approved=批准，rejected=拒绝）
    private String reason;  // 审批意见
}
```

### 2. 修改控制器方法
将`@RequestParam`改为`@RequestBody`：

```java
@PostMapping("/approve")
public JsonResult approve(@RequestBody StopSupplyApproveDto approveDto) {
    return stopSupplyApplyService.approve(
        approveDto.getId(), 
        approveDto.getStatus(), 
        approveDto.getReason()
    );
}
```

## 修复效果

### 修复前
- 前端发送JSON请求体
- 后端使用@RequestParam接收
- 参数格式不匹配
- 返回400错误

### 修复后
- 前端发送JSON请求体
- 后端使用@RequestBody接收
- 参数格式匹配
- 正常处理请求

## 技术要点

### @RequestParam vs @RequestBody

**@RequestParam**：
- 用于接收表单参数或URL参数
- 适用于`application/x-www-form-urlencoded`格式
- 参数在URL或表单中传递

**@RequestBody**：
- 用于接收JSON请求体
- 适用于`application/json`格式
- 参数在请求体中传递

### 前端请求格式
```javascript
// 使用axios发送JSON请求
this.$http.post('/api/endpoint', {
  param1: 'value1',
  param2: 'value2'
})
```

### 后端接收方式
```java
// 接收JSON请求体
@PostMapping("/endpoint")
public Result method(@RequestBody ParamDto dto) {
    // 处理逻辑
}
```

## 测试验证

### 测试步骤
1. 打开申请停供审批界面
2. 选择审批结果（批准/拒绝）
3. 输入审批意见
4. 点击"确定"提交

### 预期结果
- 不再出现400错误
- 审批成功提示
- 申请状态正确更新
- 相关账单和房屋状态按业务逻辑更新

## 相关文件

### 修改的文件
1. `StopSupplyApplyController.java` - 修改审批接口
2. `StopSupplyApproveDto.java` - 新增DTO类

### 前端文件
- `stop-supply-apply-approve.vue` - 审批弹窗组件（无需修改）

## 注意事项

1. **参数验证**：DTO类可以添加验证注解进行参数校验
2. **错误处理**：确保业务逻辑中的异常处理完善
3. **日志记录**：审批操作已添加操作日志记录
4. **事务管理**：审批操作涉及多表更新，已使用事务管理

## 扩展建议

### 1. 参数验证
可以在DTO类中添加验证注解：

```java
@Data
public class StopSupplyApproveDto {
    @NotNull(message = "申请ID不能为空")
    private Long id;
    
    @NotBlank(message = "审批状态不能为空")
    @Pattern(regexp = "approved|rejected", message = "审批状态只能是approved或rejected")
    private String status;
    
    @Size(max = 200, message = "审批意见不能超过200个字符")
    private String reason;
}
```

### 2. 统一响应格式
确保所有接口返回统一的响应格式：

```java
{
    "code": 0,
    "msg": "操作成功",
    "data": null
}
```

这次修复解决了前后端参数格式不匹配的问题，确保了申请停供审批功能的正常运行。
