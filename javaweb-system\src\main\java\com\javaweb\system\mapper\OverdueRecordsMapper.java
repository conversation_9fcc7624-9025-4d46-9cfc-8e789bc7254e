package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.system.entity.OverdueRecords;
import com.javaweb.system.query.OverdueRecordsQuery;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 欠费记录表 Mapper 接口
 * </p>
 */
public interface OverdueRecordsMapper extends BaseMapper<OverdueRecords> {

    /**
     * 获取欠费记录列表（带房屋信息）
     * @param page 分页对象
     * @param query 查询条件
     * @return
     */
    IPage<OverdueRecords> getOverdueRecordsList(Page<OverdueRecords> page, @Param("query") OverdueRecordsQuery query);

}
