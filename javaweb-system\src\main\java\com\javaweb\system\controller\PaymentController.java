package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Payment;
import com.javaweb.system.query.PaymentQuery;
import com.javaweb.system.service.IPaymentService;
import java.util.HashMap;
import java.util.Map;
import com.javaweb.system.service.IBillService;
import com.javaweb.system.service.IHouseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 缴费记录 前端控制器
 * </p>
 */
@RestController
@RequestMapping("/payment")
public class PaymentController extends BaseController {

    @Autowired
    private IPaymentService paymentService;

    @Autowired
    private IBillService billService;


    /**
     * 获取缴费记录列表
     * @param paymentQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(PaymentQuery paymentQuery) {
        return paymentService.getList(paymentQuery);
    }

    /**
     * 获取缴费记录列表（不分页，用于下拉选择）
     * @param paymentQuery 查询条件
     * @return
     */
    @GetMapping("/list")
    public JsonResult list(PaymentQuery paymentQuery) {
        return paymentService.getList(paymentQuery);
    }

    @GetMapping("/getPaymentRecords/{id}")
    public JsonResult getPaymentRecordsByBillId(@PathVariable("id")Integer id) {
        return paymentService.getPaymentRecordsByBillId(id);
    }

    /**
     * 添加缴费记录
     * @param entity 实体对象
     * @return
     */
    @Log(title = "缴费记录", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody Payment entity) {
        // 保存缴费记录
        JsonResult result = paymentService.edit(entity);
        if (result.getCode() == 0) {
            // 更新账单已缴金额和房屋供热状态
            JsonResult updateResult = paymentService.updateBillAndHouseStatus(entity);
            if (updateResult.getCode() != 0) {
                return updateResult; // 如果更新失败，返回错误信息
            }

            // 返回成功结果，包含缴费金额信息
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("paymentId", entity.getId());
            resultData.put("amount", entity.getAmount());
            resultData.put("transactionNo", entity.getTransactionNo());
            resultData.put("paymentMethod", entity.getPaymentMethod());
            resultData.put("paymentDate", entity.getPaymentDate());
            resultData.put("message", "缴费成功");

            return JsonResult.success(resultData);
        }
        return result;
    }

    /**
     * 修改缴费记录
     * @param entity 实体对象
     * @return
     */
    @Log(title = "缴费记录", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody Payment entity) {
        return paymentService.edit(entity);
    }

    /**
     * 删除缴费记录
     * @param ids ID数组
     * @return
     */
    @Log(title = "缴费记录", logType = LogType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public JsonResult delete(@PathVariable("ids") Integer[] ids) {
        return paymentService.deleteByIds(ids);
    }

    /**
     * 获取缴费记录详情
     * @param id 缴费记录ID
     * @return
     */
    @GetMapping("/info/{id}")
    public JsonResult info(@PathVariable("id") Integer id) {
        return paymentService.info(id);
    }
    
    /**
     * 根据住户地址/户号或户主姓名搜索住户信息
     * @param heatUnitId 小区ID
     * @param addressOrRoomNo 住户地址或户号
     * @param ownerName 户主姓名
     * @return 住户信息
     */
    @GetMapping("/house/search")
    public JsonResult searchHouse(Integer heatUnitId, String addressOrRoomNo, String ownerName) {
        return paymentService.searchHouse(heatUnitId, addressOrRoomNo, ownerName);
    }
    
    /**
     * 获取指定住户的缴费记录
     * @param heatUnitId 小区ID
     * @param ownerName 户主姓名
     * @param roomNo 房间号
     * @return 缴费记录列表
     */
    @GetMapping("/records")
    public JsonResult getPaymentRecords(Integer heatUnitId, String ownerName,String roomNo) {
        return paymentService.getPaymentRecords(heatUnitId, ownerName, roomNo);
    }
} 