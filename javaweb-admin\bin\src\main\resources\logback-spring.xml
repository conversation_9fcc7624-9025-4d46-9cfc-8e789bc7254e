<?xml version="1.0" encoding="UTF-8"?>
<!-- 
    logback-spring.xml配置说明：
    1. 此配置文件优先级高于application.yml中的logging配置
    2. 所有日志级别设置为ERROR，仅输出错误信息
    3. 同时配置了控制台输出和文件输出
-->
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 只输出错误日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/javaweb-admin.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>logs/javaweb-admin.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大历史记录 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 项目日志级别控制 -->
    <logger name="com.javaweb" level="ERROR" />

    <!-- 第三方库日志级别控制 -->
    <logger name="org.springframework" level="ERROR" />
    <logger name="org.apache.ibatis" level="ERROR" />
    <logger name="org.mybatis" level="ERROR" />
    <logger name="com.baomidou" level="ERROR" />
    <logger name="org.hibernate" level="ERROR" />
    <logger name="io.netty" level="ERROR" />
    <logger name="com.alibaba.druid" level="ERROR" />

    <!-- 默认日志级别 -->
    <root level="ERROR">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration> 