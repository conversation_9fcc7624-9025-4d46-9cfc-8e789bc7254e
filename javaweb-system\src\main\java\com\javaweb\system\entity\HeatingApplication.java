package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用热申请表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_heating_application")
public class HeatingApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外键，关联 t_house 表
     */
    private Long houseId;

    /**
     * 申请人姓名
     */
    private String residentName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 申请供暖季，如 2025-2026
     */
    private String applySeason;

    /**
     * 申请状态：pending=待审核, approved=已通过, rejected=已拒绝, cancelled=已取消
     */
    private String currentStatus;

    /**
     * 需要支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 申请原因（可选填）
     */
    private String applyReason;

    /**
     * 申请提交时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 审批完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;

    /**
     * 审批人（管理员账号或姓名）
     */
    private String approvedBy;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 管理员备注
     */
    private String remark;

    /**
     * 是否已发送通知：0=未通知，1=已通知
     */
    private Integer isNotified;

    /**
     * 申请来源：online=小程序在线, offline=线下窗口
     */
    private String source;

    /**
     * 记录创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 非数据库字段 - 房屋信息
     */
    @TableField(exist = false)
    private String roomNo;

    @TableField(exist = false)
    private String heatUnitName;

    @TableField(exist = false)
    private String heatUnitFloorName;

    @TableField(exist = false)
    private String heatUnitFloorUnitName;

    @TableField(exist = false)
    private String houseName;

    @TableField(exist = false)
    private String approverName;

}
