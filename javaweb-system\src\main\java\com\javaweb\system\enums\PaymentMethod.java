package com.javaweb.system.enums;

/**
 * 支付方式枚举
 * 
 * <AUTHOR>
 */
public enum PaymentMethod {
    
    /**
     * 微信支付
     */
    WECHAT("wechat", "微信支付"),
    
    /**
     * 支付宝支付
     */
    ALIPAY("alipay", "支付宝支付"),
    
    /**
     * 银行转账
     */
    BANK_TRANSFER("bank_transfer", "银行转账"),
    
    /**
     * 现金支付
     */
    CASH("cash", "现金支付");
    
    /**
     * 支付方式代码
     */
    private final String code;
    
    /**
     * 支付方式名称
     */
    private final String name;
    
    /**
     * 构造函数
     * 
     * @param code 支付方式代码
     * @param name 支付方式名称
     */
    PaymentMethod(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 获取支付方式代码
     * 
     * @return 支付方式代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取支付方式名称
     * 
     * @return 支付方式名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取支付方式枚举
     * 
     * @param code 支付方式代码
     * @return 支付方式枚举，如果找不到则返回null
     */
    public static PaymentMethod getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (PaymentMethod method : PaymentMethod.values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取支付方式枚举（不区分大小写）
     * 
     * @param name 支付方式名称
     * @return 支付方式枚举，如果找不到则返回null
     */
    public static PaymentMethod getByName(String name) {
        if (name == null) {
            return null;
        }
        
        for (PaymentMethod method : PaymentMethod.values()) {
            if (method.name().equalsIgnoreCase(name)) {
                return method;
            }
        }
        return null;
    }
    
    /**
     * 验证支付方式代码是否有效
     * 
     * @param code 支付方式代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
    
    /**
     * 验证支付方式名称是否有效
     * 
     * @param name 支付方式名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }
    
    @Override
    public String toString() {
        return this.code;
    }
}
