package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.OverdueRecords;
import com.javaweb.system.query.OverdueRecordsQuery;

/**
 * <p>
 * 欠费记录表 服务类
 * </p>
 */
public interface IOverdueRecordsService extends IService<OverdueRecords> {

    /**
     * 获取欠费记录数据列表
     * @param overdueRecordsQuery 查询条件
     * @return
     */
    JsonResult getList(OverdueRecordsQuery overdueRecordsQuery);

    /**
     * 删除欠费记录
     * @param ids ID数组
     * @return
     */
    JsonResult deleteByIds(Long[] ids);

    /**
     * 获取欠费记录详情
     * @param id 记录ID
     * @return
     */
    JsonResult info(Long id);

    /**
     * 核销欠费记录
     * @param id 记录ID
     * @param reason 核销原因
     * @return
     */
    JsonResult writeOff(Long id, String reason);

    /**
     * 批量核销欠费记录
     * @param ids ID数组
     * @param reason 核销原因
     * @return
     */
    JsonResult batchWriteOff(Long[] ids, String reason);

}
