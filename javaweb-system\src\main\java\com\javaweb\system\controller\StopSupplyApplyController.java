package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.StopSupplyApply;
import com.javaweb.system.query.StopSupplyApplyQuery;
import com.javaweb.system.service.IStopSupplyApplyService;
import com.javaweb.system.dto.StopSupplyApproveDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 申请停供 前端控制器
 * </p>
 */
@RestController
@RequestMapping("/stopSupplyApply")
public class StopSupplyApplyController extends BaseController {

    @Autowired
    private IStopSupplyApplyService stopSupplyApplyService;

    /**
     * 获取申请停供列表
     * @param stopSupplyApplyQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(StopSupplyApplyQuery stopSupplyApplyQuery) {
        return stopSupplyApplyService.getList(stopSupplyApplyQuery);
    }

    /**
     * 添加申请停供
     * @param entity 实体对象
     * @return
     */
    @Log(title = "申请停供", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody StopSupplyApply entity) {
        return stopSupplyApplyService.edit(entity);
    }

    /**
     * 修改申请停供
     * @param entity 实体对象
     * @return
     */
    @Log(title = "申请停供", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody StopSupplyApply entity) {
        return stopSupplyApplyService.edit(entity);
    }

    /**
     * 删除申请停供
     * @param ids ID数组
     * @return
     */
    @Log(title = "申请停供", logType = LogType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public JsonResult delete(@PathVariable("ids") Long[] ids) {
        return stopSupplyApplyService.deleteByIds(ids);
    }

    /**
     * 获取申请停供详情
     * @param id 申请ID
     * @return
     */
    @GetMapping("/info/{id}")
    public JsonResult info(@PathVariable("id") Long id) {
        return stopSupplyApplyService.info(id);
    }

    /**
     * 审核申请停供
     * @param approveDto 审批参数
     * @return
     */
    @Log(title = "审核申请停供", logType = LogType.UPDATE)
    @PostMapping("/approve")
    public JsonResult approve(@RequestBody StopSupplyApproveDto approveDto) {
        return stopSupplyApplyService.approve(approveDto.getId(), approveDto.getStatus(), approveDto.getReason());
    }

}
